{
  "extends": [
    "next/core-web-vitals",
    // "airbnb","airbnb/hooks",
    "eslint:recommended",
    "plugin:@typescript-eslint/eslint-recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:lodash-fp/recommended"
  ],
  "plugins": [
    "@typescript-eslint",
    "react-prefer-function-component",
    "filename-rules"
  ],
  "rules": {
    "no-shadow": "off",
    "@typescript-eslint/no-shadow": "error",
    "no-restricted-globals": "off",
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": "error",
    "react/jsx-no-useless-fragment": "off",
    "default-param-last": "off",
    "no-use-before-define": "off",
    "@typescript-eslint/no-use-before-define": [
      "error",
      { "functions": false }
    ],
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-member-accessibility": "error",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-inferrable-types": "off",
    "@typescript-eslint/no-empty-function": "off",
    "react/jsx-filename-extension": [
      "error",
      { "extensions": [".tsx", ".jsx"] }
    ],
    "react-hooks/exhaustive-deps": "error",
    "react/display-name": "off",
    "react/prop-types": "off",
    "react/require-default-props": "off",
    "react/react-in-jsx-scope": "off",
    "no-unused-expressions": ["error", { "allowTernary": true }],
    "class-methods-use-this": "off",
    "camelcase": "off",
    "curly": "error",
    "no-console": ["error", { "allow": ["error"] }],
    "quotes": [
      "error",
      "double",
      { "avoidEscape": true, "allowTemplateLiterals": false }
    ],
    "import/first": "error",
    "import/no-named-as-default": "error",
    "import/prefer-default-export": "off",
    "import/no-unassigned-import": "error",
    "react/button-has-type": "error",
    "react/destructuring-assignment": "error",
    "react/jsx-boolean-value": ["error", "never"],
    "react/no-access-state-in-setstate": "error"
  }
}
