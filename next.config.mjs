/** @type {import('next').NextConfig} */

import withTwin from "./withTwin.mjs";

const nextConfig = {
  transpilePackages: [
    "@tanstack/query-core",
    "@tanstack/react-query",
    "@tanstack/react-query-devtools",
    "react-device-detect",
    "styled-components",
  ],
  compiler: {
    styledComponents: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**",
        port: "",
        pathname: "**",
      },
    ],
  },
  env: {
    AUTH_SECRET: "BzsrAWj5cy5e0TWtSPFyRcCA2Mft8IFfdxR5Byf8Zgc=",
    REACT_SELECT_SKIP_CLIENT_INSTANCE_ID: "true",
  },

  webpack: (config, { isServer }) => {
    // Fixes npm packages that depend on `fs` module
    config.resolve.fallback = {
      fs: false,
      net: false,
      tls: false,
      "styled-jsx/server": isServer ? false : "styled-jsx/server",
    };

    config.output = {
      ...config.output,
      globalObject: "globalThis",
    };

    if (isServer) {
      const { externals } = config;
      config.externals = [
        ...(Array.isArray(externals) ? externals : [externals || ""]),
        { html3pdf: "commonjs html3pdf" },
      ];
    } else {
      // Client-side specific configurations
      // We'll rely on the environment variable set above instead of modifying plugins
    }

    return config;
  },
};

export default withTwin(nextConfig);
