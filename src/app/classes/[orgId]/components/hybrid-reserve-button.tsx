import { useClassReserve } from "@/common/hooks/useClassReserve";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Calendar, ChevronDown, Loader2 } from "lucide-react";
import { ClassDetailsResponse } from "../types";
import { getButtonText } from "../utils";

type HybridReserveButtonProps = {
  date?: string | null;
  data: ClassDetailsResponse;
  upComingClass?: boolean;
  canReserveLive?: boolean;
  canReserveVirtual?: boolean;
  canJoinWaitlist?: boolean;
};

export const HybridReserveButton = ({
  data,
  date,
  upComingClass,
  canReserveLive,
  canReserveVirtual,
  canJoinWaitlist,
}: HybridReserveButtonProps) => {
  const { allow_reservations: allowReservations } = data;
  const { isPending, isReservationAllowed, handleReserve } = useClassReserve(
    data,
    date
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <Button
          variant={isReservationAllowed ? "outline" : "destructive"}
          className='border-blue-500 w-full text-blue-500 text-xs font-bold whitespace-nowrap cursor-pointer'
          type='button'
          disabled={!allowReservations || isPending}
          style={!isReservationAllowed ? { color: "red" } : undefined}
        >
          <div className='flex justify-between items-center  w-full gap-2'>
            <div className='flex gap-2 items-center'>
              {isPending ? (
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
              ) : (
                <>
                  {allowReservations && <Calendar size={16} />}
                  {getButtonText(
                    canReserveLive,
                    canReserveVirtual,
                    canJoinWaitlist
                  )}
                </>
              )}
            </div>
            <ChevronDown size={20} />
          </div>
        </Button>
        <DropdownMenuPortal>
          <DropdownMenuContent className={!upComingClass ? "w-[210px]" : ""}>
            <DropdownMenuItem
              className='w-full cursor-pointer'
              onClick={() => handleReserve("live")}
              disabled={!canReserveLive && !canJoinWaitlist}
            >
              {canReserveLive
                ? "Reserve Live Class"
                : canJoinWaitlist
                  ? "Join Waitlist"
                  : "Reserve Live Spot (Class Full)"}
            </DropdownMenuItem>
            <DropdownMenuItem
              className='w-full cursor-pointer'
              onClick={() => handleReserve("virtual")}
              disabled={!canReserveVirtual && !canJoinWaitlist}
            >
              {canReserveVirtual
                ? "Reserve Virtually"
                : canJoinWaitlist
                  ? "Join Waitlist"
                  : "Reserve Virtually (Class Full)"}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenuPortal>
      </DropdownMenuTrigger>
    </DropdownMenu>
  );
};
