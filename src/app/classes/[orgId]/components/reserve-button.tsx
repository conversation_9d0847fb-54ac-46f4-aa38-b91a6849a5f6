import { useClassReserve } from "@/common/hooks/useClassReserve";
import { ActionButton } from "@/components/custom/action-button";
import { cn } from "@/lib/utils";
import { Calendar } from "lucide-react";
import { ClassDetailsResponse, ClassType } from "../types";

export const ReserveButton = ({
  data,
  date,
  className,
  label = "Reserve",
}: {
  data: ClassDetailsResponse;
  date?: string | null;
  className?: string;
  label?: string;
}) => {
  const { isPending, isReservationAllowed, handleReserve } = useClassReserve(
    data,
    date
  );

  const classType = data.class_type === ClassType.VIRTUAL ? "virtual" : "live";

  return (
    <ActionButton
      icon={!isReservationAllowed ? undefined : <Calendar />}
      className={cn("border-blue-500", className)}
      onClick={() => handleReserve(classType)}
      isLoading={isPending}
      isDisabled={!isReservationAllowed}
      variant={!isReservationAllowed ? "destructive" : "outline"}
      style={!isReservationAllowed ? { color: "red" } : undefined}
      {...data}
    >
      {!isReservationAllowed ? "More Info" : label}
    </ActionButton>
  );
};
