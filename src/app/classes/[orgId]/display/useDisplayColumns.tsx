"use client";

import { ColumnDef } from "@tanstack/react-table";
import { useMemo } from "react";

import { useStoreValue } from "@/app/StoreContext";
import { styled } from "styled-components";

import {
  Badge,
  NameCell,
  TimeCell,
} from "../modules/overview-block/columns/helper-components";

import { getSpotsAvailable } from "../modules/overview-block/columns/useClassesTablecolumns";
import { ClassDetailsResponse } from "../types";
import { obtainDateFrame } from "../utils";

export const StyledCell = styled.div<{ color?: string }>`
  color: ${({ color }) => color};
`;

export const useDisplayColumns = (className?: string) => {
  const { state: embedConfigs } = useStoreValue(state => state.configs);

  return useMemo<ColumnDef<ClassDetailsResponse>[]>(
    () => [
      {
        id: "time",
        accessorFn: row => obtainDateFrame(row?.start_time, row?.end_time),
        header: () => <p className={className}>TIME</p>,
        cell: ({ row }) => (
          <StyledCell
            className={className}
            color={row.original.is_sgt ? embedConfigs?.sgt_color : undefined}
          >
            <TimeCell className={className} data={row.original} />
          </StyledCell>
        ),
      },
      {
        accessorKey: "name",
        header: () => <p className={className}>CLASS</p>,
        cell: ({ row }) => (
          <StyledCell
            className={className}
            color={row.original.is_sgt ? embedConfigs?.sgt_color : undefined}
          >
            <NameCell data={row.original} />
          </StyledCell>
        ),
      },
      {
        id: "badge",
        cell: ({ row }) => <Badge data={row.original} />,
      },
      {
        id: "instructor",
        accessorKey: "instructor",
        header: () => <p className={className}>INSTRUCTOR</p>,
        cell: ({ row }) => (
          <StyledCell
            color={row.original.is_sgt ? embedConfigs?.sgt_color : undefined}
            className={`${className} ${row.original?.cancelled ? "line-through" : ""}`}
          >
            {row.original.is_class_subbed && (
              <span
                className={className}
                style={{ fontSize: 8, fontWeight: "bold" }}
              >
                SUBSTITUTE
              </span>
            )}
            <p className={className}>
              {row.original?.is_class_subbed
                ? row.original.subbing_instructor
                : row?.original?.instructor}
            </p>
          </StyledCell>
        ),
      },
      {
        id: "room_name",
        accessorKey: "room_name",
        header: () => <p className={className}>ROOM</p>,
        cell: ({ row }) => (
          <StyledCell
            color={row.original.is_sgt ? embedConfigs?.sgt_color : undefined}
            className={`${className} ${row.original?.cancelled ? "line-through" : ""}`}
          >
            {row?.original?.room_name}
          </StyledCell>
        ),
      },
      {
        id: "spots",
        accessorKey: "spots",
        header: () => <p className={className}>SPOTS</p>,
        cell: ({ row }) => (
          <StyledCell
            className={className}
            color={row.original.is_sgt ? embedConfigs?.sgt_color : undefined}
          >
            {getSpotsAvailable(row.original)}
          </StyledCell>
        ),
      },
    ],
    [className, embedConfigs?.sgt_color]
  );
};
