"use client";

import { useParams, useSearchParams } from "next/navigation";

import { DataTable } from "@/components/data-table/data-table";

import { useClassesData } from "./useClassesData";
import { TimeClock } from "../../../../components/custom/time-clock";
import { useStoreValue } from "@/app/StoreContext";
import { Option } from "@/components/ui/select";
import Image from "next/image";
import { useDisplayColumns } from "./useDisplayColumns";
import { useFeatureFlags } from "@/components/custom/feature-flags/feature-flags";
import { useMemo } from "react";

export const ClassesOverview = ({
  searchParams,
  clientLogo,
  facilitiesOptions,
}: {
  clientLogo?: string;
  facilitiesOptions?: Option[];
  searchParams: Record<string, string | number>;
}) => {
  const { orgId } = useParams();
  const { data, isPending } = useClassesData(orgId as string, searchParams);

  const flags = useFeatureFlags();

  const columns = useDisplayColumns("font-semibold text-2xl");

  const filteredColumns = useMemo(() => {
    const inactiveColumnIds = new Set(
      (flags.flags ?? []).filter(flag => !flag.isActive).map(flag => flag.name)
    );
    return columns.filter(
      column => !inactiveColumnIds.has(column.id as string)
    );
  }, [columns, flags]);

  const selectedFacility = String(useSearchParams().get("gym_id"));

  const facility = facilitiesOptions?.find(
    ({ value }) => value === selectedFacility
  )?.label;

  const { state: configs } = useStoreValue(state => state.configs);

  return (
    <div className='p-6 flex flex-col h-max-content  text-3xl font-semibold'>
      <div className='mb-4 grid grid-cols-3 items-center w-full'>
        <div className='col-span-1'></div> {/* Empty div for spacing */}
        {Boolean(configs?.display_page_clock) && (
          <div className='flex justify-center items-center'>
            <TimeClock color={configs?.accent_color} font={configs?.fontName} />
          </div>
        )}
        {Boolean(configs?.display_page_logo) && (
          <div className='flex justify-end w-full'>
            <div className='flex flex-col items-center'>
              {clientLogo && (
                <Image
                  className='rounded-full w-82 h-82'
                  src={String(clientLogo)}
                  alt='client-logo'
                  width={80}
                  height={80}
                  style={{ color: configs?.accent_color }}
                />
              )}
              {facility && (
                <p
                  style={{ color: configs?.accent_color }}
                  className='text-center text-2xl mt-2 pr-1'
                >
                  {facility}
                </p>
              )}
            </div>
          </div>
        )}
      </div>
      <DataTable
        classNames='pb-16 font-semibold'
        showFilters={false}
        isLoading={isPending}
        data={data ?? []}
        columns={filteredColumns}
      />
    </div>
  );
};
