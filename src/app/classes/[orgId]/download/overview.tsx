"use client";

import { useClassesWeeklyData } from "@/app/classes/[orgId]/queries/useClassesWeeklyData";
import type { ClassDetailsResponse } from "@/app/classes/[orgId]/types";
import { formatDate } from "@/common/common.utils";
import { ALL_OPTION } from "@/components/custom/activity-filters";
import { PDFDownloadButton } from "@/components/custom/pdf-creator/download";
import dynamic from "next/dynamic";

import { useWeekDates } from "@/common/hooks/useWeekDates";
import { usePDFFilters } from "@/components/pdf-downloader-module/usePDFFilters";
import { Button } from "@/components/ui/button";
import { BaseSelect, Option } from "@/components/ui/select";
import { format } from "date-fns";
import { X } from "lucide-react";

const ClassPDFCreator = dynamic(
  () =>
    import("@/components/custom/pdf-creator/creator").then(mod => ({
      default: mod.ClassPDFCreator as React.ComponentType<{
        data: ClassDetailsResponse[];
        clientName?: string;
        weekPeriod?: string;
        activityType?: string;
        configs?: Record<string, string>;
        logo?: string;
        header?: string;
        category?: string;
        iosUrl?: string;
        androidUrl?: string;
        clientBackground?: string;
        clientId?: string;
      }>,
    })),
  { ssr: false }
);

export const ClassPDFOverview = ({
  categoriesOptions = [],
  facilitiesOptions = [],
  clientName,
  clientId,
  clientLogo,
  clientPdfHeader,
  appUrls,
  clientBackground,
}: {
  categoriesOptions: Option[];
  facilitiesOptions: Option[];
  clientName?: string;
  clientId?: string;
  clientLogo?: string;
  clientPdfHeader?: string;
  appUrls?: Record<string, string>;
  clientBackground?: string;
}) => {
  const { field, handleChange, handleClose, enableRequest, configs } =
    usePDFFilters();

  const { weekStart, weekEnd } = useWeekDates();
  const formatStartWeek = format(weekStart, "MMM d, uuuu");
  const formatEndOfWeek = format(weekEnd, "MMM d, uuuu");

  const {
    data,
    isFetching,
    isPending: isLoading,
  } = useClassesWeeklyData(
    clientId as string,
    {
      gym_id: field.gym_id === ALL_OPTION ? "" : field.gym_id,
      class_category_id:
        field.class_category_id === ALL_OPTION ? "" : field.class_category_id,
      start_date: formatDate(weekStart),
      end_date: formatDate(weekEnd),
    },
    enableRequest // enable request only when modal is open
  );

  const activity = facilitiesOptions.find(
    fac => fac.value === field.gym_id
  )?.label;

  const category = categoriesOptions.find(
    cat => cat.value === field.class_category_id
  )?.label;

  return (
    <>
      <div className='text-center'>
        <p className='text-center'>Download weekly schedule</p>
        <p className='text-center pt-4 font-bold'>
          {formatStartWeek} - {formatEndOfWeek}
        </p>

        {!isLoading && data?.length === 0 ? (
          <div className='text-red-500 text-center'>
            <p>There is no data to download</p>
            <p> Please select a different location</p>
          </div>
        ) : (
          <p className='text-center'>Please select location to download</p>
        )}
      </div>
      <div className='my-5 flex flex-col gap-4'>
        <BaseSelect
          className='w-full'
          onChange={handleChange}
          placeholder='Select Location'
          options={facilitiesOptions}
          value={field?.gym_id}
          name='gym_id'
          menuPlacement='bottom'
        />
        <BaseSelect
          className='w-full'
          onChange={handleChange}
          placeholder={"Select Categories"}
          options={categoriesOptions}
          value={field?.class_category_id}
          name='class_category_id'
          menuPlacement='bottom'
        />
      </div>
      <div className='flex justify-between'>
        <Button onClick={() => handleClose()} variant={"outline"}>
          <X className='mr-2 w-4 hover:bg-white' color='blue' />
          <span>Cancel</span>
        </Button>
        <PDFDownloadButton
          isLoading={isFetching}
          disabled={!data?.length}
          filename={`${clientName}-schedule-${formatStartWeek}-${formatEndOfWeek}.pdf`}
          content={
            <ClassPDFCreator
              data={data || []}
              clientName={clientName}
              weekPeriod={`${formatStartWeek} - ${formatEndOfWeek}`}
              activityType={activity}
              configs={configs}
              logo={clientLogo}
              header={clientPdfHeader}
              category={category}
              androidUrl={appUrls?.android}
              iosUrl={appUrls?.ios}
              clientBackground={clientBackground}
              clientId={clientId}
            />
          }
        />
      </div>
    </>
  );
};
