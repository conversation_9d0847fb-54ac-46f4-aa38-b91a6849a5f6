import { ClassDetailsResponse, ClassType } from "../types";
import { getClassAvailabilityAction } from "./getClassAvailabilityAction";

describe("getClassAvailabilityAction", () => {
  const createMockClass = (overrides = {}): ClassDetailsResponse =>
    ({
      id: 1,
      gym_id: 1,
      room_id: 1,
      start_time: "09:00:00",
      end_time: "10:00:00",
      name: "Test Class",
      description: "Test Description",
      allow_reservation_date: "2024-01-01",
      advance_time: 24,
      spots: 10,
      walkin_spots: 0,
      walkin_spots_available: false,
      is_sgt: 0,
      class_category_id: 1,
      tags: [],
      days_of_week: [],
      allow_waitlist: 1,
      is_waitlist_available: false,
      waitlist_spots: 5,
      gym_name: "Test Gym",
      room_name: "Test Room",
      slot_id: 1,
      category: "Test Category",
      instructor_id: 1,
      instructor_first_name: "<PERSON>",
      instructor_last_name: "Instructor",
      facility_closed: false,
      cancelled: false,
      allow_reservations: true,
      instructor: "Test Instructor",
      is_class_subbed: false,
      reservation_count: 0,
      waitlist_count: 0,
      waitlist_spots_available: 5,
      occupancy_total: 0,
      images: [],
      class_type: ClassType.LIVE,
      virtual_spots_available: 0,
      spots_available: 0,
      is_full: false,
      is_past: false,
      current_user_waitlist: undefined,
      current_user_reservation: undefined,
      ...overrides,
    }) as const;

  const defaultResponse = {
    canReserve: false,
    canJoinWaitlist: false,
    canReserveLive: false,
    isOnReservation: false,
    canReserveVirtual: false,
    showBothVirtualAndLiveReserve: false,
    isOnWaitlist: false,
    hasWalkinSpots: false,
    isPast: false,
    isCancelled: false,
    isFullWithNoWaitlist: false,
    isFullWithWaitlist: false,
  };

  describe("Early returns", () => {
    it("should return isOnReservation when user has reservation", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          current_user_reservation: { id: 1 },
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        isOnReservation: true,
      });
    });

    it("should return isOnWaitlist when user is on waitlist", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          current_user_waitlist: { id: 1 },
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        isOnWaitlist: true,
      });
    });

    it("should return hasWalkinSpots when class is full with walk-in spots", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          is_full: true,
          walkin_spots_available: true,
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        hasWalkinSpots: true,
      });
    });

    it("should return default when reservations are not allowed", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          allow_reservations: false,
        })
      );
      expect(result).toEqual(defaultResponse);
    });

    it("should return default for invalid class type", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          class_type: "INVALID" as ClassType,
        })
      );
      expect(result).toEqual(defaultResponse);
    });
  });

  describe("Live classes", () => {
    it("should allow live reservation when spots available", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          class_type: ClassType.LIVE,
          spots_available: 1,
          is_full: false,
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        canReserve: true,
        canReserveLive: true,
      });
    });

    it("should allow waitlist when full and waitlist available", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          class_type: ClassType.LIVE,
          spots_available: 0,
          is_waitlist_available: true,
          is_full: true,
          walkin_spots_available: false,
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        canJoinWaitlist: true,
        isFullWithWaitlist: true,
      });
    });

    it("should return default when full and no waitlist", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          class_type: ClassType.LIVE,
          spots_available: 0,
          is_waitlist_available: false,
          is_full: true,
          walkin_spots_available: false,
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        isFullWithNoWaitlist: true,
      });
    });
  });

  describe("Virtual classes", () => {
    it("should allow virtual reservation when spots available", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          class_type: ClassType.VIRTUAL,
          virtual_spots_available: 1,
          is_full: false,
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        canReserve: true,
        canReserveVirtual: true,
      });
    });

    it("should allow waitlist when full and waitlist available", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          class_type: ClassType.VIRTUAL,
          virtual_spots_available: 0,
          is_waitlist_available: true,
          is_full: true,
          walkin_spots_available: false,
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        canJoinWaitlist: true,
        isFullWithWaitlist: true,
      });
    });

    it("should show full with no waitlist when full and no waitlist", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          class_type: ClassType.VIRTUAL,
          virtual_spots_available: 0,
          is_waitlist_available: false,
          is_full: true,
          walkin_spots_available: false,
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        isFullWithNoWaitlist: true,
      });
    });

    it("should return default when not full and no spots", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          class_type: ClassType.VIRTUAL,
          virtual_spots_available: 0,
          is_full: false,
        })
      );
      expect(result).toEqual(defaultResponse);
    });
  });

  describe("Hybrid classes", () => {
    it("should show both options when both spots available", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          class_type: ClassType.HYBRID,
          spots_available: 1,
          virtual_spots_available: 1,
          is_waitlist_available: true,
          is_full: false,
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        canReserve: true,
        canReserveLive: true,
        canReserveVirtual: true,
        showBothVirtualAndLiveReserve: true,
        canJoinWaitlist: true,
      });
    });

    it("should show both options when both spots available but no waitlist", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          class_type: ClassType.HYBRID,
          spots_available: 1,
          virtual_spots_available: 1,
          is_waitlist_available: false,
          is_full: false,
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        canReserve: true,
        canReserveLive: true,
        canReserveVirtual: true,
        showBothVirtualAndLiveReserve: true,
        canJoinWaitlist: false,
      });
    });

    it("should show live only when only live spots available", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          class_type: ClassType.HYBRID,
          spots_available: 1,
          virtual_spots_available: 0,
          is_full: false,
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        canReserve: true,
        canReserveLive: true,
        canReserveVirtual: false,
        showBothVirtualAndLiveReserve: true,
        canJoinWaitlist: false,
      });
    });

    it("should show virtual only when only virtual spots available", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          class_type: ClassType.HYBRID,
          spots_available: 0,
          virtual_spots_available: 1,
          is_full: false,
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        canReserve: true,
        canReserveLive: false,
        canReserveVirtual: true,
        showBothVirtualAndLiveReserve: true,
        canJoinWaitlist: false,
      });
    });

    it("should show full with no waitlist when full and waitlist available", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          class_type: ClassType.HYBRID,
          spots_available: 0,
          virtual_spots_available: 0,
          is_waitlist_available: true,
          is_full: true,
          walkin_spots_available: false,
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        isFullWithWaitlist: true,
        isFullWithNoWaitlist: false,
      });
    });

    it("should return default when both spots full and no waitlist", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          class_type: ClassType.HYBRID,
          spots_available: 0,
          virtual_spots_available: 0,
          is_waitlist_available: false,
          is_full: true,
          walkin_spots_available: false,
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        isFullWithNoWaitlist: true,
      });
    });
  });

  describe("Edge cases", () => {
    it("should handle undefined values", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          spots_available: undefined,
          virtual_spots_available: undefined,
          is_waitlist_available: undefined,
          current_user_waitlist: undefined,
          current_user_reservation: undefined,
        } as any)
      );
      expect(result).toEqual(defaultResponse);
    });

    it("should handle null waitlist status", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          class_type: ClassType.LIVE,
          spots_available: 0,
          is_waitlist_available: true,
          is_full: true,
          walkin_spots_available: false,
          current_user_waitlist: null,
        } as any)
      );
      expect(result).toEqual({
        ...defaultResponse,
        canJoinWaitlist: true,
        isFullWithWaitlist: true,
      });
    });

    it("should handle past classes", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          is_past: true,
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        isPast: true,
      });
    });

    it("should handle cancelled classes", () => {
      const result = getClassAvailabilityAction(
        createMockClass({
          cancelled: true,
        })
      );
      expect(result).toEqual({
        ...defaultResponse,
        isCancelled: true,
      });
    });
  });
});
