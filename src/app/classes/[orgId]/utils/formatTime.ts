import { format, parse } from "date-fns";

export const formatTime = (time: string) => {
  if (!time) {
    return "-";
  }

  const splittedTime = time.split(" ");

  const timeSplitted = splittedTime[1] ?? splittedTime[0] ?? "";

  const parsedTime = parse(timeSplitted, "HH:mm:ss", new Date());

  // To allow no space between time and am/pm
  return format(parsedTime, "h:mmaaa");
};

export const obtainDateFrame = (startTime: string, endTime: string) => {
  if (startTime?.length === 5) {
    startTime += ":00";
  }

  if (endTime?.length === 5) {
    endTime += ":00";
  }
  return `${formatTime(startTime)} - ${formatTime(endTime)}`;
};

export const formatHourMinsTime = (time: string) => {
  if (!time) {
    return "";
  }

  if (time.length === 5) {
    time += ":00";
  }

  const parsedTime = parse(time, "HH:mm:ss", new Date());
  return format(parsedTime, "HH:mm");
};

export const formatTimeToHHMM = (time: string) => {
  if (!time) {
    return "";
  }

  if (time?.length === 5) {
    time += ":00";
  }

  return formatTime(time);
};
