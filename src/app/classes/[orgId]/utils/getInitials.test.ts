import { getInitials } from "./getInitials";

describe("getInitials", () => {
  it("should return initials for a single word", () => {
    expect(getInitials("<PERSON>")).toBe("J");
  });

  it("should return initials for two words", () => {
    expect(getInitials("John Doe")).toBe("JD");
  });

  it("should return initials for multiple words", () => {
    expect(getInitials("John Middle Doe")).toBe("JMD");
  });

  it("should handle empty string", () => {
    expect(getInitials("")).toBe("");
  });

  it("should handle spaces", () => {
    expect(getInitials("   John   Doe   ")).toBe("JD");
  });

  it("should handle lowercase names", () => {
    expect(getInitials("john doe")).toBe("JD");
  });

  it("should handle mixed case names", () => {
    expect(getInitials("<PERSON> D<PERSON>")).toBe("JD");
  });

  it("should handle undefined input", () => {
    expect(getInitials(undefined)).toBe("");
  });

  it("should handle null input", () => {
    expect(getInitials(null)).toBe("");
  });
});
