import { ClassDetailsResponse, ClassType } from "../types";

export interface ClassAvailabilityAction {
  canReserve: boolean;
  showBothVirtualAndLiveReserve: boolean;
  isOnWaitlist: boolean;
  isOnReservation: boolean;
  hasWalkinSpots: boolean;
  isPast: boolean;
  isCancelled: boolean;
  isFullWithNoWaitlist: boolean;
  isFullWithWaitlist: boolean;
  canReserveLive: boolean;
  canReserveVirtual: boolean;
  canJoinWaitlist: boolean;
}

export const getClassAvailabilityAction = (
  classDetails: ClassDetailsResponse
): ClassAvailabilityAction | undefined => {
  const {
    class_type: classType,
    spots_available: liveSpots = 0,
    virtual_spots_available: virtualSpots = 0,
    is_waitlist_available: isWaitlistAvailable = false,
    current_user_waitlist: currentUserOnWaitlist = false,
    current_user_reservation: currentUserOnReservation = false,
    walkin_spots_available: hasWalkinSpots = false,
    is_full: isFull = false,
    is_past: isPast = false,
    cancelled: isCancelled = false,
    allow_reservations: isReservationAllowed = true,
  } = classDetails;

  const defaultResponse: ClassAvailabilityAction = {
    isPast,
    isCancelled,
    canReserve: false,
    isOnWaitlist: false,
    canReserveLive: false,
    isOnReservation: false,
    canReserveVirtual: false,
    canJoinWaitlist: false,
    showBothVirtualAndLiveReserve: false,
    hasWalkinSpots: isFull && hasWalkinSpots,
    isFullWithNoWaitlist: isFull && !isWaitlistAvailable && !hasWalkinSpots,
    isFullWithWaitlist: isFull && isWaitlistAvailable && !hasWalkinSpots,
  };

  // Early return if user already has a reservation
  if (currentUserOnReservation) {
    return { ...defaultResponse, isOnReservation: true };
  }

  // Early return if user is already on waitlist
  if (currentUserOnWaitlist) {
    return { ...defaultResponse, isOnWaitlist: true };
  }

  // Early return if class has walk-in spots and is full
  if (isFull && hasWalkinSpots) {
    return defaultResponse;
  }

  // Early return if reservations are not allowed
  if (!isReservationAllowed) {
    return defaultResponse;
  }

  // Handle invalid class type
  if (!Object.values(ClassType).includes(classType)) {
    return defaultResponse;
  }

  const hasLiveSpots = liveSpots > 0;
  const hasVirtualSpots = virtualSpots > 0;

  switch (classType) {
    case ClassType.LIVE: {
      if (hasLiveSpots) {
        return { ...defaultResponse, canReserve: true, canReserveLive: true };
      }

      if (isFull && isWaitlistAvailable) {
        return {
          ...defaultResponse,
          canJoinWaitlist: true,
          isFullWithWaitlist: true,
        };
      }

      return defaultResponse;
    }

    case ClassType.VIRTUAL: {
      if (hasVirtualSpots) {
        return {
          ...defaultResponse,
          canReserve: true,
          canReserveVirtual: true,
        };
      }

      if (isFull && isWaitlistAvailable) {
        return {
          ...defaultResponse,
          canJoinWaitlist: true,
          isFullWithWaitlist: true,
        };
      }

      if (isFull && !isWaitlistAvailable) {
        return {
          ...defaultResponse,
          isFullWithNoWaitlist: true,
        };
      }

      return defaultResponse;
    }

    case ClassType.HYBRID: {
      if (hasLiveSpots || hasVirtualSpots) {
        return {
          ...defaultResponse,
          canReserve: true,
          canReserveLive: hasLiveSpots,
          canReserveVirtual: hasVirtualSpots,
          showBothVirtualAndLiveReserve: true,
          canJoinWaitlist: isWaitlistAvailable,
        };
      }

      if (isFull && isWaitlistAvailable) {
        return {
          ...defaultResponse,
          isFullWithWaitlist: true,
        };
      }

      return defaultResponse;
    }

    default:
      return defaultResponse;
  }
};
