/**
 * Gets the appropriate button text for a hybrid class based on reservation availability
 */
export const getButtonText = (
  canReserveLive: boolean | undefined,
  canReserveVirtual: boolean | undefined,
  canJoinWaitlist: boolean | undefined
): string => {
  const canLive = canReserveLive;
  const canVirtual = canReserveVirtual;
  const canWaitlist = canJoinWaitlist;

  // If nothing is available
  if (!canLive && !canVirtual && !canWaitlist) {
    return "Class Full";
  }

  if (!canWaitlist && !canLive && canVirtual) {
    return "Reserve";
  }

  // If only waitlist is available
  if (canWaitlist && !canLive) {
    return "Join Waitlist";
  }

  // For all other cases (hybrid, live-only, or virtual-only)
  return "Reserve";
};
