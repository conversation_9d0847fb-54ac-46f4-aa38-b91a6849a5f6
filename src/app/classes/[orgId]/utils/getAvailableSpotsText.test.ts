import { ClassDetailsResponse, ClassType } from "../types";
import { getAvailableSpotsText } from "./getAvailableSpotsText";

describe("getAvailableSpotsText", () => {
  const createMockClass = (
    overrides: Partial<ClassDetailsResponse> = {}
  ): ClassDetailsResponse => ({
    id: 1,
    class_type: ClassType.LIVE,
    spots_available: 0,
    virtual_spots_available: 0,
    is_full: false,
    walkin_spots_available: false,
    gym_id: 1,
    room_id: 1,
    start_time: "",
    end_time: "",
    name: "",
    description: "",
    allow_reservation_date: "",
    advance_time: 0,
    spots: 0,
    walkin_spots: 0,
    is_sgt: 0,
    class_category_id: 0,
    tags: [],
    days_of_week: [],
    allow_waitlist: 0,
    waitlist_spots: 0,
    gym_name: "",
    room_name: "",
    slot_id: 0,
    category: "",
    instructor_id: 0,
    instructor_first_name: "",
    instructor_last_name: "",
    facility_closed: false,
    cancelled: false,
    allow_reservations: true,
    instructor: "",
    is_class_subbed: false,
    reservation_count: 0,
    waitlist_count: 0,
    waitlist_spots_available: 0,
    occupancy_total: 0,
    images: [],
    ...overrides,
  });

  describe("Walk-in spots", () => {
    it("should show walk-in spots when class is full and has walk-in spots", () => {
      const result = getAvailableSpotsText(
        createMockClass({
          is_full: true,
          walkin_spots_available: true,
        })
      );
      expect(result).toBe("Walk-ins welcome");
    });

    it("should not show walk-in spots when class is not full", () => {
      const result = getAvailableSpotsText(
        createMockClass({
          walkin_spots_available: true,
          spots_available: 1,
        })
      );
      expect(result).toBe("1 spot left");
    });
  });

  describe("Live classes", () => {
    it("should show correct message for 0 spots", () => {
      const result = getAvailableSpotsText(
        createMockClass({
          class_type: ClassType.LIVE,
          spots_available: 0,
        })
      );
      expect(result).toBe("0 spot left");
    });

    it("should show correct message for 1 spot", () => {
      const result = getAvailableSpotsText(
        createMockClass({
          class_type: ClassType.LIVE,
          spots_available: 1,
        })
      );
      expect(result).toBe("1 spot left");
    });

    it("should show correct message for multiple spots", () => {
      const result = getAvailableSpotsText(
        createMockClass({
          class_type: ClassType.LIVE,
          spots_available: 5,
        })
      );
      expect(result).toBe("5 spots left");
    });
  });

  describe("Virtual classes", () => {
    it("should show correct message for 0 spots", () => {
      const result = getAvailableSpotsText(
        createMockClass({
          class_type: ClassType.VIRTUAL,
          virtual_spots_available: 0,
        })
      );
      expect(result).toBe("0 spot left");
    });

    it("should show correct message for 1 spot", () => {
      const result = getAvailableSpotsText(
        createMockClass({
          class_type: ClassType.VIRTUAL,
          virtual_spots_available: 1,
        })
      );
      expect(result).toBe("1 spot left");
    });

    it("should show correct message for multiple spots", () => {
      const result = getAvailableSpotsText(
        createMockClass({
          class_type: ClassType.VIRTUAL,
          virtual_spots_available: 5,
        })
      );
      expect(result).toBe("5 spots left");
    });
  });

  describe("Hybrid classes", () => {
    it("should prioritize live spots when both are available", () => {
      const result = getAvailableSpotsText(
        createMockClass({
          class_type: ClassType.HYBRID,
          spots_available: 3,
          virtual_spots_available: 2,
        })
      );
      expect(result).toBe("3 spots left");
    });

    it("should show virtual spots when no live spots available", () => {
      const result = getAvailableSpotsText(
        createMockClass({
          class_type: ClassType.HYBRID,
          spots_available: 0,
          virtual_spots_available: 2,
        })
      );
      expect(result).toBe("2 spots left");
    });

    it("should show 0 spots when both live and virtual are 0", () => {
      const result = getAvailableSpotsText(
        createMockClass({
          class_type: ClassType.HYBRID,
          spots_available: 0,
          virtual_spots_available: 0,
        })
      );
      expect(result).toBe("0 spot left");
    });
  });

  describe("Edge cases", () => {
    it("should handle invalid class type", () => {
      const result = getAvailableSpotsText(
        createMockClass({
          class_type: "INVALID" as ClassType,
        })
      );
      expect(result).toBe("");
    });

    it("should handle undefined values", () => {
      const result = getAvailableSpotsText(
        createMockClass({
          spots_available: undefined,
          virtual_spots_available: undefined,
        })
      );
      expect(result).toBe("0 spot left");
    });
  });
});
