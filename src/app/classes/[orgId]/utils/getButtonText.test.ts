import { getButtonText } from "./getButtonText";

describe("getButtonText", () => {
  it('should return "Reserve" when both live and virtual spots are available', () => {
    expect(getButtonText(true, true, false)).toBe("Reserve");
  });

  it('should return "Join Waitlist" when only waitlist is available', () => {
    expect(getButtonText(false, false, true)).toBe("Join Waitlist");
  });

  it('should return "Reserve" when only live spots are available', () => {
    expect(getButtonText(true, false, false)).toBe("Reserve");
  });

  it('should return "Reserve" when only virtual spots are available', () => {
    expect(getButtonText(false, true, false)).toBe("Reserve");
  });

  it('should return "Class Full" when nothing is available', () => {
    expect(getButtonText(false, false, false)).toBe("Class Full");
  });

  describe("Edge cases", () => {
    it("should handle undefined values", () => {
      expect(getButtonText(undefined, undefined, undefined)).toBe("Class Full");
      expect(getButtonText(true, undefined, undefined)).toBe("Reserve");
      expect(getButtonText(undefined, true, undefined)).toBe("Reserve");
      expect(getButtonText(undefined, undefined, true)).toBe("Join Waitlist");
    });

    it("should handle mixed undefined and boolean values", () => {
      expect(getButtonText(true, true, undefined)).toBe("Reserve");
      expect(getButtonText(false, true, undefined)).toBe("Reserve");
      expect(getButtonText(undefined, true, true)).toBe("Join Waitlist");
      expect(getButtonText(true, undefined, true)).toBe("Reserve");
    });
  });
});
