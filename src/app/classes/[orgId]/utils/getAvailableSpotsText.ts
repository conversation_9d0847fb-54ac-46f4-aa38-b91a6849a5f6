import { ClassDetailsResponse, ClassType } from "../types";

const formatSpotsText = (spots: number): string => {
  if (spots === 0) {
    return "0 spot left";
  }
  return `${spots} spot${spots > 1 ? "s" : ""} left`;
};

const WALKIN_SPOTS_TEXT = "Walk-ins welcome";

export const getAvailableSpotsText = (
  classDetails: ClassDetailsResponse
): string => {
  const {
    class_type: classType,
    is_full: isFull,
    virtual_spots_available: virtualSpots = 0,
    spots_available: liveSpots = 0,
    walkin_spots_available: hasWalkinSpots,
  } = classDetails;

  if (isFull && hasWalkinSpots) {
    return WALKIN_SPOTS_TEXT;
  }

  switch (classType) {
    case ClassType.LIVE:
      return formatSpotsText(liveSpots);

    case ClassType.VIRTUAL:
      return formatSpotsText(virtualSpots);

    case ClassType.HYBRID: {
      if (liveSpots > 0) {
        return formatSpotsText(liveSpots);
      }
      if (virtualSpots > 0) {
        return formatSpotsText(virtualSpots);
      }
      if (liveSpots === 0 && virtualSpots === 0) {
        return "0 spot left";
      }
      return "";
    }

    default:
      return "";
  }
};
