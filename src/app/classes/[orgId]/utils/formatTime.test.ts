import { formatHourMinsTime, formatTime, obtainDateFrame } from "./formatTime";

describe("formatTime", () => {
  it("should format time correctly", () => {
    expect(formatTime("14:30:00")).toBe("2:30pm");
    expect(formatTime("09:15:00")).toBe("9:15am");
    expect(formatTime("00:00:00")).toBe("12:00am");
    expect(formatTime("23:59:59")).toBe("11:59pm");
  });

  it("should handle empty input", () => {
    expect(formatTime("")).toBe("-");
    expect(formatTime(undefined as any)).toBe("-");
  });

  it("should handle time with spaces", () => {
    expect(formatTime("2023-01-01 14:30:00")).toBe("2:30pm");
  });
});

describe("obtainDateFrame", () => {
  it("should format time range correctly", () => {
    expect(obtainDateFrame("09:00:00", "10:30:00")).toBe("9:00am - 10:30am");
    expect(obtainDateFrame("14:00:00", "15:30:00")).toBe("2:00pm - 3:30pm");
  });

  it("should handle 5-digit times", () => {
    expect(obtainDateFrame("09:00", "10:30")).toBe("9:00am - 10:30am");
  });

  it("should handle mixed formats", () => {
    expect(obtainDateFrame("09:00:00", "10:30")).toBe("9:00am - 10:30am");
    expect(obtainDateFrame("09:00", "10:30:00")).toBe("9:00am - 10:30am");
  });
});

describe("formatHourMinsTime", () => {
  it("should format time in 24-hour format", () => {
    expect(formatHourMinsTime("14:30:00")).toBe("14:30");
    expect(formatHourMinsTime("09:15:00")).toBe("09:15");
    expect(formatHourMinsTime("00:00:00")).toBe("00:00");
    expect(formatHourMinsTime("23:59:59")).toBe("23:59");
  });

  it("should handle 5-digit times", () => {
    expect(formatHourMinsTime("14:30")).toBe("14:30");
    expect(formatHourMinsTime("09:15")).toBe("09:15");
  });

  it("should handle empty input", () => {
    expect(formatHourMinsTime("")).toBe("");
    expect(formatHourMinsTime(undefined as any)).toBe("");
  });
});
