"use client";

import { FirebaseEvent } from "@/lib/firebase/config";
import { useFirebase } from "@/lib/firebase/FirebaseProvider";
import { useEffect } from "react";

interface PageViewTrackerProps {
  pageTitle: string;
  event?: FirebaseEvent;
}

export function EmbedPageViewTracker({
  pageTitle,
  event = FirebaseEvent.EMBED_LOADED,
}: PageViewTrackerProps) {
  const { logEvent } = useFirebase();

  useEffect(() => {
    try {
      logEvent(event, {
        page_title: pageTitle,
        page_location: window.location.href,
        page_path: window.location.pathname,
      });
    } catch {
      /* empty */
    }
  }, [logEvent, pageTitle, event]);

  return null;
}
