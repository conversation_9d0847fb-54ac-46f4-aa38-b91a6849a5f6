"use client";
import { Calendar } from "lucide-react";
import { ClassDetailsResponse } from "../../../types";

import { useSearchParams } from "next/navigation";

import { useStoreValue } from "@/app/StoreContext";

import { ActionButton } from "@/components/custom/action-button";
import { useSession } from "@/components/custom/login-auth/auth-provider";

import Link from "next/link";
import { CancelButton } from "../../../../../../components/custom/cancel-button";
import { HybridReserveButton } from "../../../components/hybrid-reserve-button";
import { LeaveWaitlistButton } from "../../../components/leave-reservation";
import { ReserveButton } from "../../../components/reserve-button";
import { getClassAvailabilityAction } from "../../../utils";

const SessionAvailable = ({
  data,
  upComingClass,
}: {
  data: ClassDetailsResponse;
  upComingClass?: boolean;
}) => {
  const {
    current_user_reservation: currentUserReservation,
    current_user_waitlist: currentUserWaitlist,
  } = data;

  const {
    canReserve,
    isOnWaitlist,
    isOnReservation,
    canReserveLive,
    canReserveVirtual,
    canJoinWaitlist,
    isFullWithWaitlist,
    showBothVirtualAndLiveReserve,
  } = getClassAvailabilityAction(data) ?? {};

  const date = useSearchParams().get("date");

  const { dispatch } = useStoreValue();

  if (isOnWaitlist) {
    return (
      <LeaveWaitlistButton
        id={currentUserWaitlist?.id}
        positionIndex={currentUserWaitlist?.position_index}
      />
    );
  }

  if (isFullWithWaitlist && !currentUserReservation) {
    return (
      <ReserveButton
        data={data}
        date={data?.date ?? date}
        className='!border-lightOrange-500 !text-lightOrange-500'
        label='Join Waitlist'
      />
    );
  }

  if (isOnReservation) {
    return (
      <CancelButton
        onClick={() =>
          dispatch(() => ({
            cancelReservation: { ...currentUserReservation },
          }))
        }
      />
    );
  }

  if (canReserve) {
    if (showBothVirtualAndLiveReserve) {
      return (
        <HybridReserveButton
          data={data}
          date={data?.date ?? date}
          upComingClass={upComingClass}
          canReserveLive={canReserveLive}
          canJoinWaitlist={canJoinWaitlist}
          canReserveVirtual={canReserveVirtual}
        />
      );
    } else {
      return <ReserveButton data={data} date={data?.date ?? date} />;
    }
  }

  return null;
};

export const SessionNotAvailable = ({
  data,
}: {
  data: ClassDetailsResponse;
}) => {
  const { isFullWithNoWaitlist } = getClassAvailabilityAction(data) ?? {};

  const { dispatch } = useStoreValue(state => state.shouldShowLoginModal);

  if (isFullWithNoWaitlist) {
    return (
      <ActionButton
        onClick={() => dispatch(() => ({ shouldShowLoginModal: true }))}
        icon={<Calendar />}
        className='border-blue-500'
      >
        Login for waitlist
      </ActionButton>
    );
  }

  return (
    <ActionButton
      onClick={() => dispatch(() => ({ shouldShowLoginModal: true }))}
      icon={<Calendar />}
      className='border-blue-500'
    >
      Login to reserve
    </ActionButton>
  );
};

export const LoginAction = ({
  data,
  upComingClass = false,
}: {
  data: ClassDetailsResponse;
  upComingClass?: boolean;
}) => {
  const { data: sessionData } = useSession();

  const { state: configs } = useStoreValue(state => state.configs);
  const { isPast, hasWalkinSpots, isCancelled, isFullWithNoWaitlist } =
    getClassAvailabilityAction(data) ?? {};

  if (isFullWithNoWaitlist) {
    return (
      <ActionButton
        variant='default'
        isDisabled
        className='!bg-gray-200 text-black cursor-not-allowed'
      >
        Class Full
      </ActionButton>
    );
  }

  if (isPast) {
    return (
      <ActionButton
        variant='default'
        isDisabled
        className='!bg-gray-200 text-black cursor-not-allowed'
      >
        Past Class – Reservation Closed
      </ActionButton>
    );
  }

  if (hasWalkinSpots) {
    return (
      <ActionButton
        variant='default'
        isDisabled
        className='!bg-gray-200 text-black cursor-not-allowed'
      >
        Walk-ins Spots Available
      </ActionButton>
    );
  }

  if (isCancelled) {
    return (
      <ActionButton
        variant='default'
        isDisabled
        className='!bg-gray-200 !text-black cursor-not-allowed'
      >
        Cancelled
      </ActionButton>
    );
  }

  return sessionData ? (
    <div className='flex flex-col'>
      <SessionAvailable data={data} upComingClass={upComingClass} />
      {Boolean(data.is_sgt && configs?.sgtPurchaseUrl) && (
        <Link
          onClick={e => e.stopPropagation()}
          href={configs?.sgtPurchaseUrl ?? ""}
          target='__blank'
          className='text-center font-thin text-xs mb-1 mt-2'
          style={{
            color: configs?.sgt_color ?? "",
            fontWeight: "bold",
          }}
        >
          Purchase here
        </Link>
      )}
    </div>
  ) : (
    <div className='flex flex-col'>
      <SessionNotAvailable data={data} />
      {Boolean(data.is_sgt && configs?.sgtPurchaseUrl) && (
        <Link
          onClick={e => e.stopPropagation()}
          href={configs?.sgtPurchaseUrl ?? ""}
          target='__blank'
          className='text-center font-thin text-xs mb-1 mt-1'
          style={{
            color: configs?.sgt_color ?? "",
            fontWeight: "bold",
          }}
        >
          Purchase here
        </Link>
      )}
    </div>
  );
};
