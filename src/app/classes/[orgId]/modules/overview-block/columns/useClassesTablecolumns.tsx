"use client";

import { ColumnDef } from "@tanstack/react-table";
import { useMemo } from "react";
import { ClassDetailsResponse } from "../../../types";

import { useStoreValue } from "@/app/StoreContext";
import { SortArrow } from "@/components/custom/column-header-sort";
import { styled } from "styled-components";
import { obtainDateFrame } from "../../../utils";
import { LoginAction } from "./action-cell";
import { Badge, NameCell, TimeCell } from "./helper-components";

export const StyledCell = styled.div<{ color?: string }>`
  color: ${({ color }) => color};
`;

export const getSpotsAvailable = (data: ClassDetailsResponse) => {
  if (data.cancelled) {
    return "";
  }

  if (data.walkin_spots_available && data.is_full) {
    return " walk-ins available";
  }

  return `${data?.spots_available} spots left`;
};

export const useClassesTableColumns = (className?: string) => {
  const { state: embedConfigs } = useStoreValue(state => state.configs);

  return useMemo<ColumnDef<ClassDetailsResponse>[]>(
    () => [
      {
        id: "time",
        accessorFn: row => obtainDateFrame(row?.start_time, row?.end_time),
        header: () => <p className={className}>TIME</p>,
        cell: ({ row }) => (
          <StyledCell
            className={className}
            color={row.original.is_sgt ? embedConfigs?.sgt_color : undefined}
          >
            <TimeCell className={className} data={row.original} />
          </StyledCell>
        ),
      },
      {
        accessorKey: "name",
        header: ({ column }) => (
          <SortArrow
            className={className}
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            sortDirection={column.getIsSorted()}
            columnName='CLASS'
          />
        ),
        cell: ({ row }) => (
          <StyledCell
            className={className}
            color={row.original.is_sgt ? embedConfigs?.sgt_color : undefined}
          >
            <NameCell data={row.original} />
          </StyledCell>
        ),
      },
      {
        id: "badge",
        cell: ({ row }) => <Badge className={className} data={row.original} />,
      },
      {
        accessorKey: "instructor",
        header: ({ column }) => {
          return (
            <SortArrow
              onClick={() =>
                column.toggleSorting(column.getIsSorted() === "asc")
              }
              className={className}
              sortDirection={column.getIsSorted()}
              columnName='INSTRUCTOR'
            />
          );
        },
        cell: ({ row }) => (
          <StyledCell
            color={row.original.is_sgt ? embedConfigs?.sgt_color : undefined}
            className={`${className} ${row.original?.cancelled ? "line-through" : ""}`}
          >
            {row.original.is_class_subbed && (
              <span
                className={className}
                style={{ fontSize: 8, fontWeight: "bold" }}
              >
                SUBSTITUTE
              </span>
            )}
            <p className={className}>
              {row.original?.is_class_subbed
                ? row.original.subbing_instructor
                : row?.original?.instructor}
            </p>
          </StyledCell>
        ),
      },
      {
        accessorKey: "gym_name",
        id: "location",
        header: ({ column }) => {
          return (
            <SortArrow
              onClick={() =>
                column.toggleSorting(column.getIsSorted() === "asc")
              }
              className={className}
              sortDirection={column.getIsSorted()}
              columnName='LOCATION'
            />
          );
        },
        cell: ({ row }) => (
          <StyledCell
            color={row.original.is_sgt ? embedConfigs?.sgt_color : undefined}
            className={`${className} ${row.original?.cancelled ? "line-through" : ""}`}
          >
            {row.original?.gym_name}
          </StyledCell>
        ),
      },
      {
        accessorKey: "room_name",
        header: ({ column }) => {
          return (
            <SortArrow
              onClick={() =>
                column.toggleSorting(column.getIsSorted() === "asc")
              }
              className={className}
              sortDirection={column.getIsSorted()}
              columnName='ROOM'
            />
          );
        },
        cell: ({ row }) => (
          <StyledCell
            color={row.original.is_sgt ? embedConfigs?.sgt_color : undefined}
            className={`${className} ${row.original?.cancelled ? "line-through" : ""}`}
          >
            {row?.original?.room_name}
          </StyledCell>
        ),
      },

      {
        accessorKey: "spots",
        header: ({ column }) => (
          <SortArrow
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            sortDirection={column.getIsSorted()}
            columnName='SPOTS'
            className={className}
          />
        ),
        cell: ({ row }) => {
          return (
            <StyledCell
              className={className}
              color={row.original.is_sgt ? embedConfigs?.sgt_color : undefined}
            >
              {row.original.available_slots_text}
            </StyledCell>
          );
        },
      },
      {
        id: "action",
        cell: ({ row }) => <LoginAction data={row?.original} />,
      },
    ],
    [className, embedConfigs?.sgt_color]
  );
};
