import {
  defaultGetStatus,
  statusColorMap,
  statusTextMap,
} from "@/components/custom/column-badge";
import { Badge as ShadBadge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Star } from "lucide-react";
import { ClassDetailsResponse } from "../../../types";
import { obtainDateFrame } from "../../../utils";

// TimeCell component
export const TimeCell = ({
  data,
  className,
}: {
  data: ClassDetailsResponse;
  className?: string;
}) => {
  return (
    <div
      className={cn(
        "text-sm mr-8 w-full",
        className,
        data?.cancel_reason ? "line-through" : ""
      )}
    >
      {obtainDateFrame(data?.start_time, data?.end_time)}
    </div>
  );
};

export const Recommended = ({ className }: { className?: string }) => {
  return (
    <div className='flex'>
      <Star
        fill='#EEA124'
        color='#EEA124'
        className={cn("mr-1 mt-1  h-3 w-3", className)}
      />
      <p style={{ fontSize: 8, fontWeight: "bold" }}>RECOMMENDED</p>
    </div>
  );
};

// Name cell
export const NameCell = ({ data }: { data: ClassDetailsResponse }) => {
  const { is_recommended, name } = data;

  return (
    <div className='flex justify-between'>
      <div>
        {is_recommended && <Recommended />}
        <p className={`${data.cancelled ? "line-through" : ""} `}>{name}</p>
      </div>
    </div>
  );
};

export const Badge = ({
  data,
}: {
  data: ClassDetailsResponse;
  className?: string;
}) => {
  const {
    is_waitlist_available,
    is_full,
    current_user_reservation,
    walkin_spots_available,
  } = data;

  const foundStatus = defaultGetStatus(
    data as unknown as Record<string, string>
  );

  let badgeInfo = null as { text: string; color: string } | null;

  if (foundStatus) {
    badgeInfo = {
      text: statusTextMap[foundStatus],
      color: statusColorMap[foundStatus],
    };
  }

  return (
    <div>
      {is_full && walkin_spots_available ? (
        <ShadBadge className={" hover:bg-green-600 bg-green-600"}>
          <span style={{ fontSize: 11, fontWeight: "bold" }}>Walk-ins</span>
        </ShadBadge>
      ) : (
        <div className={"flex flex-col"}>
          {is_full && is_waitlist_available && !current_user_reservation && (
            <p
              className='whitespace-nowrap'
              style={{ fontSize: 8, fontWeight: "bold", whiteSpace: "nowrap" }}
            >
              WAITLIST AVAIL.
            </p>
          )}
          {badgeInfo ? (
            <ShadBadge className={cn(" w-fit", badgeInfo.color)}>
              <span style={{ fontSize: 11, fontWeight: "bold" }}>
                {badgeInfo.text}
              </span>
            </ShadBadge>
          ) : null}
        </div>
      )}
    </div>
  );
};
