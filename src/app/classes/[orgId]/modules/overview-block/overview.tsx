"use client";
import { ClassesDataTable } from "./data-table";

import { omit } from "lodash/fp";
// import { ReserveSuccessModal } from "./reserve-success-modal";
import { useStoreValue } from "@/app/StoreContext";
import { CancelReservationModal } from "./cancel-class-modal";
import { ReserveSuccessModal } from "./reserve-success-modal";

export const ClassesOverview = ({
  searchParams,
}: {
  searchParams: Record<string, string | number>;
}) => {
  const omittedSelectedClass = omit("selectedClass", searchParams);

  const { state: reservations, dispatch } = useStoreValue(
    state => state.reservation
  );

  return (
    <div className='container rounded-lg border-2  p-6'>
      <ClassesDataTable searchParams={omittedSelectedClass} />

      <CancelReservationModal />

      {reservations && (
        <ReserveSuccessModal
          reservations={reservations}
          onClose={() => dispatch(() => ({ reservation: null }))}
          paramDate={searchParams?.date as string}
        />
      )}
    </div>
  );
};
