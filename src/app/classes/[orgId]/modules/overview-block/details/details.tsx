import { Drawer, DrawerContent, DrawerFooter } from "@/components/ui/drawer";
import { ClassDetailsResponse } from "../../../types";

import { DoorClosed, Users, X } from "lucide-react";

import { IoLocationOutline } from "react-icons/io5";

import { ColumnBadge } from "@/components/custom/column-badge";
import { InfoDetailsRow } from "@/components/custom/info-details-rows";
import { Button } from "@/components/ui/button";
import { getAvailableSpotsText } from "../../../utils";
import { LoginAction } from "../columns/action-cell";
import { DayBadgeList } from "./day-badge-list";
import { ImageCard } from "./details-image";
import { Instructors } from "./instructor";
import { UpcomingClass } from "./upcoming-class";

export const ColumnDetails = ({
  isOpen,
  onOpen,
  rowData,
  selectedDate,
}: {
  isOpen: boolean;
  rowData: ClassDetailsResponse;
  onOpen: (open: boolean) => void;
  selectedDate?: string;
}) => {
  return (
    <Drawer direction='right' open={isOpen} onOpenChange={onOpen}>
      <DrawerContent className='right-0 w-full lg:mx-w-[500px] md:w-[500px] h-full rounded-none border-white overflow-y-auto overflow-x-hidden outline-none'>
        <div className='bg-white text-gray-800 pl-3 pr-3  h-full overflow-y-auto overflow-x-hidden pb-20'>
          <div className='p-4 border-b border-gray-300 text-right'>
            <Button onClick={() => onOpen(false)} variant={"ghost"}>
              Close <X className='ml-1 w-4' />
            </Button>
          </div>
          <ImageCard
            imageSrc={rowData?.images?.[0] as string}
            startDay={selectedDate as string}
            startTime={rowData?.start_time}
            endTime={rowData?.end_time}
          />
          <div className='p-4'>
            <div className='flex space-x-2 mb-4'>
              <ColumnBadge
                status={rowData as unknown as Record<string, string>}
              />
            </div>
            <h1 className='text-2xl font-bold mb-4'>{rowData.name}</h1>
            <DayBadgeList rowData={rowData} />
            <div className='mt-4 border-t border-gray-300'>
              <InfoDetailsRow icon={<Users />}>
                {rowData?.category}
              </InfoDetailsRow>
              <InfoDetailsRow icon={<IoLocationOutline className='w-6 h-6' />}>
                {rowData.is_virtual ? "Virtual Class" : "Live Class"}
              </InfoDetailsRow>
              <InfoDetailsRow icon={<DoorClosed />}>
                {rowData?.room_name}
              </InfoDetailsRow>
            </div>
          </div>
          <Instructors
            primaryInstructor={rowData?.instructor}
            subInstructor={rowData?.subbing_instructor}
          />
          {rowData?.description && (
            <div className='p-4'>
              <h2 className='text-lg font-semibold mb-2'>What to expect:</h2>
              <p className='text-gray-600 mb-4'>{rowData?.description}</p>
            </div>
          )}
          <UpcomingClass data={rowData} />
        </div>

        <DrawerFooter className='fixed bottom-0 bg-gray-100 w-full py-6 grid grid-cols-2 items-center  z-50'>
          <p className='pl-3'>{getAvailableSpotsText(rowData)}</p>
          <LoginAction data={rowData} />
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
};
