"use client";
import { formatDate } from "@/common/common.utils";
import { CircleLoader } from "@/components/custom/loader";
import { useSession } from "@/components/custom/login-auth/auth-provider";
import { addDays, format, parseISO } from "date-fns";
import { useSearchParams } from "next/navigation";
import { CancelButton } from "../../../../../../components/custom/cancel-button";
import { LeaveWaitlistButton } from "../../../components/leave-reservation";
import { ReserveButton } from "../../../components/reserve-button";
import { useUserReservations } from "../../../queries/useUserReservations";
import { ClassDetailsResponse } from "../../../types";
import { formatTime } from "../../../utils";
import { SessionNotAvailable } from "../columns/action-cell";

const getNextTwoDays = (currentDate: Date, daysOfWeek: string[]) => {
  const days = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"];
  const today = format(currentDate, "eee").toLowerCase();
  const todayIndex = days.indexOf(today);
  const nextTwoDays: { day?: string; date?: string }[] = [];

  let i = 1;
  while (nextTwoDays.length < 2) {
    const nextDayIndex = (todayIndex + i) % 7;
    const nextDay = days[nextDayIndex] ?? "";
    if (daysOfWeek.includes(nextDay)) {
      nextTwoDays.push({
        day: formatDate(addDays(currentDate, i)),
        date: format(addDays(currentDate, i), "EEEE, MMMM d"),
      });
    }
    i++;
  }

  return nextTwoDays;
};

export const UpcomingClass = ({ data }: { data: ClassDetailsResponse }) => {
  const searchParams = useSearchParams();

  const date = searchParams.get("date") ?? "";

  const { data: allData, isPending } = useUserReservations(true);

  const { data: sessionData } = useSession();

  const { days_of_week } = data;
  const nextTwoDays = getNextTwoDays(
    date ? parseISO(date) : parseISO(new Date().toISOString()),
    days_of_week
  );

  return (
    <div className='p-4 border-t border-gray-300 mb-6'>
      <h2 className='text-lg font-semibold mb-2'>Upcoming Classes:</h2>

      {isPending && <CircleLoader />}

      {allData &&
        nextTwoDays.map((dayInfo, index) => {
          const foundReservation = allData
            ?.filter(rec => rec.class_id === data.id)
            ?.find(rec => rec.start_time.split(" ")[0] === dayInfo.day);

          return (
            <div
              key={index}
              className='container rounded-md border-2 py-4 mt-4 gap-2 mb-2'
            >
              <div className='flex justify-between items-center gap-6'>
                <div className='whitespace-nowrap'>
                  <p>{data.name}</p>
                  <p className='text-sm mt-1'>{dayInfo.date}</p>
                  <p className='text-sm mt-1'>{`${formatTime(data.start_time)}-${formatTime(data.end_time)}`}</p>
                </div>

                {sessionData ? (
                  foundReservation ? (
                    foundReservation.position_index ? (
                      <LeaveWaitlistButton
                        id={foundReservation.id}
                        positionIndex={foundReservation.position_index}
                      />
                    ) : (
                      <CancelButton
                        id={foundReservation.id}
                        className='!w-40'
                      />
                    )
                  ) : (
                    <ReserveButton
                      data={data}
                      date={dayInfo.day}
                      className='!w-28 ml-2'
                    />
                  )
                ) : (
                  <div className='!w-40 ml-2'>
                    <SessionNotAvailable data={data} />
                  </div>
                )}
              </div>
            </div>
          );
        })}
    </div>
  );
};
