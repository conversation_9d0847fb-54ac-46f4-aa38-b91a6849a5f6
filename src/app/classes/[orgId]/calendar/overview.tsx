"use client";

import { TableFilters } from "@/components/data-table/table-filters";

import { useStoreValue } from "@/app/StoreContext";
import { useWeekDates } from "@/common/hooks/useWeekDates";
import { format } from "date-fns";
import { matchSorter } from "match-sorter";
import { useState } from "react";
import { CalendarLoader } from "../../../../components/custom/calendar-loader";
import { CancelReservationModal } from "../modules/overview-block/cancel-class-modal";
import { ReserveSuccessModal } from "../modules/overview-block/reserve-success-modal";
import { useClassesWeeklyData } from "../queries/useClassesWeeklyData";
import { ClassDetailsResponse } from "../types";
import { ClassCalendar } from "./calendar";
import { ClassModal } from "./details-modal";

export const ClassCalendarOverview = ({
  orgId,
  searchParams,
}: {
  orgId: string;
  searchParams: Record<string, string | number>;
}) => {
  const { weekStart, weekEnd } = useWeekDates();

  const { data, isPending } = useClassesWeeklyData(
    orgId,
    {
      ...searchParams,
      start_date: format(weekStart, "yyyy-MM-dd"),
      end_date: format(weekEnd, "yyyy-MM-dd"),
    },
    true
  );

  const [selected, setSelected] = useState<ClassDetailsResponse | undefined>(
    undefined
  );

  const { state: reservations, dispatch } = useStoreValue(
    state => state.reservation
  );

  const [searchTerm, setSearchTerm] = useState("");

  const filteredData = searchTerm
    ? matchSorter(data ?? [], searchTerm, {
        keys: ["name", "instructor", "room_name", "gym_name", "start_time"],
      })
    : data;

  return (
    <div className='relative z-0 mb-4'>
      <TableFilters
        placeholder='Search by class, name, room, time or instructor'
        searchTerm={searchTerm}
        onSearchedClasses={setSearchTerm}
      />

      {isPending ? (
        <CalendarLoader />
      ) : (
        <ClassCalendar
          weekStart={weekStart}
          data={filteredData}
          selected={selected}
          setSelected={setSelected}
        />
      )}
      {selected && (
        <ClassModal
          isOpen={Boolean(selected)}
          setIsOpen={() => setSelected(undefined)}
          classDetails={selected}
          orgId={orgId}
        />
      )}

      {reservations && (
        <ReserveSuccessModal
          reservations={reservations}
          onClose={() => {
            setSelected(undefined);
            dispatch(() => ({ reservation: null }));
          }}
          paramDate={reservations?.date as string}
        />
      )}

      {selected && (
        <CancelReservationModal onClose={() => setSelected(undefined)} />
      )}
    </div>
  );
};
