"use client";
import { useStoreValue } from "@/app/StoreContext";
import { ActionButton } from "@/components/custom/action-button";
import { useSession } from "@/components/custom/login-auth/auth-provider";
import {
  LoginForm,
  LoginSchema,
} from "@/components/custom/login-auth/login-form";
import { useState } from "react";
import { z } from "zod";
import { useReserveMutation } from "../mutations/useReserveClass";
import { ClassDetailsResponse } from "../types";
import { getAvailableSpotsText, getClassAvailabilityAction } from "../utils";

export const LoginToReserve = ({
  orgId,
  data,
}: {
  orgId?: string;
  data: ClassDetailsResponse;
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const { signIn, isError, errorMessage } = useSession();

  const { dispatch } = useStoreValue();
  const { hasWalkinSpots, isFullWithNoWaitlist } =
    getClassAvailabilityAction(data) ?? {};

  const {
    mutate: handleReservation,
    isSuccess: isReserveSuccess,
    isPending,
  } = useReserveMutation(() =>
    dispatch(() => ({
      reservation: { ...data },
    }))
  );

  const handleSubmit = async (fieldValue: z.infer<typeof LoginSchema>) => {
    try {
      setIsLoading(true);
      await signIn({
        email: fieldValue.email,
        password: fieldValue.password,
        orgId,
      });

      handleReservation({
        date: data.date,
        class_id: data.id,
        type: "class",
      });
    } catch (error) {
      throw new Error("Something bad happened, try again");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='container mb-5'>
      {data.is_past ? (
        <ActionButton style={{ background: "grey", color: "black" }} isDisabled>
          Past Class – Reservation Closed
        </ActionButton>
      ) : isFullWithNoWaitlist ? (
        <p className='flex justify-center bg-gray-400 rounded-sm font-bold p-2 text-white'>
          Class Full
        </p>
      ) : !hasWalkinSpots ? (
        <>
          <p className='flex justify-center bg-gray-400 rounded-sm font-bold p-2 text-white'>
            {getAvailableSpotsText(data)}
          </p>
          {!isReserveSuccess && (
            <LoginForm
              handleSubmit={handleSubmit}
              isError={isError}
              placeholder='Login to Reserve'
              isLoading={isPending || isLoading}
              className='lg:w-full'
              errorMessage={errorMessage}
            />
          )}
        </>
      ) : null}
    </div>
  );
};
