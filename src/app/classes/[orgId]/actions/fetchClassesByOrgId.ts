"use client";

import { ClassDetailsResponse } from "@/app/classes/[orgId]/types";
import { formatDate } from "@/common/common.utils";
import {
  getSession,
  signOut,
} from "@/components/custom/login-auth/auth-provider";
import { BASE_API_URL_CLIENT } from "./constant";

export const fetchClassesByOrgId = async ({
  orgId,
  params,
  signal,
  xSource,
  isExcludeParams = false,
}: {
  orgId: string;
  params: Record<string, string | number>;
  signal?: AbortSignal;
  xSource?: string;
  isExcludeParams?: boolean;
}): Promise<ClassDetailsResponse[]> => {
  let classesURL = `${BASE_API_URL_CLIENT}/classes/list/unauth`;

  const session = await getSession();
  const headers: {
    "Content-Type": string;
    authorization?: string;
    "X-SOURCE": string;
  } = {
    "Content-Type": "application/json",
    "X-SOURCE": xSource ? "DISPLAY-EMBED" : "EMBED",
  };

  if (session) {
    headers.authorization = `Bearer ${session?.token}`;
    classesURL = `${BASE_API_URL_CLIENT}/classes/reservations/list`;
  }

  try {
    const urlParams = new URLSearchParams({
      ...params,
      university_id: orgId,
      date: formatDate(params?.date as string),
    }).toString();

    const excludeParam = isExcludeParams
      ? ""
      : "&exclude_if_closed_or_cancelled=true&exclude_past_classes=true";

    const rec = await fetch(`${classesURL}?${urlParams}${excludeParam}`, {
      headers,
      signal,
    });

    const data = await rec.json();

    if (data?.code === 401) {
      await signOut();
      return [];
    }

    return data?.classes || [];
  } catch (err) {
    throw new Error("Could not fetch classes");
  }
};
