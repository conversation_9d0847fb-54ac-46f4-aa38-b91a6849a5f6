"use client";

import { ReactNode, createContext, useContext, useRef } from "react";
import { createStore, useStore } from "zustand";
// import { persist } from "zustand/middleware";
import { Option } from "@/components/ui/select";

export type StoreState = {
  shouldShowLoginModal: boolean;
  showDownloadPDF?: boolean;
  startTimeOptions?: Option[];
  endTimeOptions?: Option[];
  configs?: Record<string, string>;
  reservation?: Record<string, unknown> | null;
  cancelReservation?: Record<string, unknown> | null;
  showMyReservations?: boolean;
  selectedFacility?: string;
};

export type StoreActions = {
  setLoginModal: (show: boolean) => void;
  setDownloadPDF: (show: boolean) => void;
  setReservation: (reservation: Record<string, unknown> | null) => void;
  setCancelReservation: (reservation: Record<string, unknown> | null) => void;
  setShowMyReservations: (show: boolean) => void;
  setSelectedFacility: (facilityId: string) => void;
  updateState: (updates: Partial<StoreState>) => void;
};

// Define the type for our store
type StoreType = ReturnType<typeof createAppStore>;

// Create a safer storage implementation
// const createSafeStorage = () => {
//   if (typeof window !== "undefined") {
//     return window.sessionStorage;
//   }

//   // Return a no-op storage for SSR
//   return {
//     getItem: () => null,
//     setItem: () => undefined,
//     removeItem: () => undefined,
//   };
// };

/**
 * Create a function to create the store with actions
 */
const createAppStore = (initialState?: Partial<StoreState>) => {
  const DEFAULT_STATE: StoreState = {
    shouldShowLoginModal: false,
  };

  return createStore<StoreState & StoreActions>()(
    // persist(
    set => ({
      ...DEFAULT_STATE,
      ...initialState,

      // Actions
      setLoginModal: show => set({ shouldShowLoginModal: show }),
      setDownloadPDF: show => set({ showDownloadPDF: show }),
      setReservation: reservation => set({ reservation }),
      setCancelReservation: reservation =>
        set({ cancelReservation: reservation }),
      setShowMyReservations: show => set({ showMyReservations: show }),
      setSelectedFacility: facilityId => set({ selectedFacility: facilityId }),
      updateState: updates => set(updates),
    })
    // {
    //   // name: "app-state-storage",
    //   // storage: createJSONStorage(createSafeStorage),
    // }
  );
  // );
};

export const StoreContext = createContext<StoreType | null>(null);

export const StoreProvider = ({
  initialState,
  children,
}: {
  initialState: StoreState;
  children: ReactNode;
}) => {
  const storeRef = useRef<StoreType>();
  if (!storeRef.current) {
    storeRef.current = createAppStore(initialState);
  }

  return (
    <StoreContext.Provider value={storeRef.current}>
      {children}
    </StoreContext.Provider>
  );
};

/**
 * Custom hook to use the store from context with type safety
 */
export const useAppStore = <T,>(
  selector: (state: StoreState & StoreActions) => T
): T => {
  const store = useContext(StoreContext);
  if (!store) {
    throw new Error("Missing StoreProvider in the tree");
  }
  return useStore(store, selector);
};

/**
 * Simplified hook that provides both state and actions
 * This maintains backward compatibility with existing code
 */
export const useStoreValue = <S,>(selector?: (state: StoreState) => S) => {
  const store = useContext(StoreContext);
  if (!store) {
    throw new Error("Missing StoreProvider in the tree");
  }

  const selectedState = useStore(store, state =>
    selector ? selector(state) : (undefined as unknown as S)
  );

  const dispatch = (updater: (state: StoreState) => Partial<StoreState>) => {
    store.getState().updateState(updater(store.getState()));
  };

  return {
    state: selectedState,
    dispatch,
  };
};
