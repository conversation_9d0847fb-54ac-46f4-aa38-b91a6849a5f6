"use client";

import { BASE_API_URL_CLIENT } from "@/app/classes/[orgId]/actions/constant";
import { formatDate } from "@/common/common.utils";
import {
  getSession,
  signOut,
} from "@/components/custom/login-auth/auth-provider";
import { EquipmentProps } from "../types";

export const fetchEquipmentByOrgId = async (
  orgId: string,
  params: Record<string, string | number>,
  signal?: AbortSignal
): Promise<EquipmentProps[]> => {
  let equipmentURL = `${BASE_API_URL_CLIENT}/equipment/list/unauth`;

  const session = await getSession();
  const headers: {
    "Content-Type": string;
    authorization?: string;
    "X-SOURCE": string;
  } = {
    "Content-Type": "application/json",
    "X-SOURCE": "EMBED",
  };

  if (session) {
    headers.authorization = `Bearer ${session?.token}`;
    equipmentURL = `${BASE_API_URL_CLIENT}/equipment/list`;
  }

  try {
    const urlParams = new URLSearchParams({
      ...params,
      university_id: orgId,
      date: formatDate(params?.date as string),
    }).toString();

    const rec = await fetch(
      `${equipmentURL}?${urlParams}&exclude_if_closed_or_cancelled=true`,
      {
        headers,
        signal,
      }
    );

    const data = await rec.json();

    if (data?.code === 401) {
      await signOut();
      return [];
    }

    return data?.equipment || [];
  } catch (err) {
    throw new Error("Could not fetch equipment");
  }
};
