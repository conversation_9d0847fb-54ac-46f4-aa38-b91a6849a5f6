import { EmbedPageViewTracker } from "@/app/classes/[orgId]/modules/analytics/EmbedPageViewTracker";
import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { EquipmentOverview } from "./overview";

export default async function Display({
  searchParams,
  params,
}: Readonly<{
  params: {
    orgId: string;
  };
  searchParams: Record<string, string | number>;
}>) {
  const { scheduleUrl } = await fetchClientConfigs(params?.orgId);

  return (
    <div className='overflow-hidden'>
      <EmbedPageViewTracker pageTitle='Equipment Display Page' />
      <EquipmentOverview
        searchParams={searchParams}
        scheduleUrl={scheduleUrl}
      />
    </div>
  );
}
