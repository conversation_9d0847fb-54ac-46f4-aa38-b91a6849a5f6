"use client";

import { obtainDateFrame } from "@/app/classes/[orgId]/utils";
import { uniqueId } from "lodash/fp";
import { Fragment, useMemo } from "react";
import { ScheduleTable } from "../../../../components/custom/schedule-table";
import { EquipmentSchedule, EquipmentSlot } from "../types";

const CalendarView = ({
  slots = [],
  column,
}: {
  slots: EquipmentSlot[];
  column: string;
}) => {
  const filteredSlots = slots?.filter(slot => slot?.day_of_week === column);

  return (
    <div className='bg-gray-100'>
      {filteredSlots?.map(slot => (
        <div key={uniqueId("slot_")} className='p-4'>
          <div className='block text-sm font-bold text-gray-700'>
            {obtainDateFrame(slot.start_time, slot.end_time)}
          </div>
        </div>
      ))}
    </div>
  );
};

function defaultIsGreyed(item: EquipmentSchedule, columnId: string): boolean {
  return Boolean(item?.slots?.find(slot => slot.day_of_week === columnId));
}

export const Calendar = ({
  data,
  color,
  category,
  activity,
  isLoading,
}: {
  data: EquipmentSchedule[];
  color?: string;
  activity?: string;
  category?: string;
  isLoading?: boolean;
}) => {
  const columns = useMemo(
    () => [
      {
        id: "",
        label: "",
        renderCell: () => "",
      },
      {
        id: "Sun",
        label: "SUNDAY",
        renderCell: (item: EquipmentSchedule, column: string) => (
          <CalendarView slots={item?.slots} column={column} />
        ),
        isGreyed: defaultIsGreyed,
      },
      {
        id: "Mon",
        label: "MONDAY",
        renderCell: (item: EquipmentSchedule, column: string) => (
          <CalendarView slots={item?.slots} column={column} />
        ),
        isGreyed: defaultIsGreyed,
      },
      {
        id: "Tue",
        label: "TUESDAY",
        renderCell: (item: EquipmentSchedule, column: string) => (
          <CalendarView slots={item?.slots} column={column} />
        ),
        isGreyed: defaultIsGreyed,
      },
      {
        id: "Wed",
        label: "WEDNESDAY",
        renderCell: (item: EquipmentSchedule, column: string) => (
          <CalendarView slots={item?.slots} column={column} />
        ),
        isGreyed: defaultIsGreyed,
      },
      {
        id: "Thu",
        label: "THURSDAY",
        renderCell: (item: EquipmentSchedule, column: string) => (
          <CalendarView slots={item?.slots} column={column} />
        ),
        isGreyed: defaultIsGreyed,
      },
      {
        id: "Fri",
        label: "FRIDAY",
        renderCell: (item: EquipmentSchedule, column: string) => (
          <CalendarView slots={item?.slots} column={column} />
        ),
        isGreyed: defaultIsGreyed,
      },
      {
        id: "Sat",
        label: "SATURDAY",
        renderCell: (item: EquipmentSchedule, column: string) => (
          <CalendarView slots={item?.slots} column={column} />
        ),
        isGreyed: defaultIsGreyed,
      },
    ],
    []
  );

  return (
    <div className='p-6 mb-12'>
      <ScheduleTable
        columns={columns}
        data={data || []}
        color={color}
        renderEmptyCell={item => <Fragment>{item?.equipment?.name}</Fragment>}
        isLoading={isLoading}
        emptyStateMessage={`No data available for Facility : ${activity ?? "All facilities"} and Schedule Type: ${category ?? "All categories"}`}
      />
    </div>
  );
};
