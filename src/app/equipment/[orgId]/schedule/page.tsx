import { EmbedPageViewTracker } from "@/app/classes/[orgId]/modules/analytics/EmbedPageViewTracker";
import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { Footer } from "@/components/custom/footer";
import { FirebaseEvent } from "@/lib/firebase/config";
import { SchedulePage } from "./schedule";

export default async function Schedule({
  params,
  searchParams,
}: Readonly<{
  params: {
    orgId: string;
  };
  searchParams: Record<string, string | number>;
}>) {
  const {
    facilitiesOptions,
    equipmentCategoriesOptions,
    appUrls,
    clientName,
    clientLogo,
    clientPdfHeader,
  } = await fetchClientConfigs(params.orgId);

  return (
    <div className='overflow-auto h-screen'>
      <EmbedPageViewTracker
        pageTitle='Equipment Schedule Page'
        event={FirebaseEvent.SCHEDULE_VIEW_OPENED}
      />
      <SchedulePage
        facilitiesOptions={facilitiesOptions}
        categoriesOptions={equipmentCategoriesOptions}
        clientName={clientName}
        clientId={params.orgId}
        clientLogo={clientLogo}
        clientPdfHeader={clientPdfHeader}
        appUrls={appUrls}
        searchParams={searchParams}
      />
      <Footer
        appUrls={appUrls}
        infoText='Make reservations today on the go! Download the app today:'
      />
    </div>
  );
}
