import Image from "next/image";
import { SelectedEquipmentSchedule } from "../types";
import DateCard from "./date-card";

export default function ImageCard({
  selected,
}: {
  selected: SelectedEquipmentSchedule;
}) {
  const { selectedDate, equipment } = selected;
  const { image_url: imageUrl } = equipment;

  return (
    <>
      {!imageUrl && (
        <div className='relative h-[80px] mt-0 md:rounded-lg md:overflow-hidden'>
          <DateCard
            selectedDate={selectedDate}
            className='border border-gray-200'
          />
        </div>
      )}
      {imageUrl && (
        <div className='relative h-[200px] md:h-[220px] lg:h-[230px] mt-0 md:rounded-lg md:overflow-hidden'>
          <Image
            className='absolute inset-0 h-full z-3 w-full rounded md:rounded-lg object-cover'
            src={imageUrl}
            height={300}
            width={500}
            alt='upace-image'
            priority
          />
          <DateCard
            selectedDate={selectedDate}
            className='absolute bottom-2 left-4 md:bottom-3 md:left-4 border-none bg-white'
          />
        </div>
      )}
    </>
  );
}
