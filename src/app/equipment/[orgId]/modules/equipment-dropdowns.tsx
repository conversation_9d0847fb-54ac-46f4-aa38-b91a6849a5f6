import { isDateInPast } from "@/common/common.utils";
import { useSession } from "@/components/custom/login-auth/auth-provider";
import { Option } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { LoginToReserve } from "../calendar/login-reserve";
import { useEquipmentAvailableTime } from "../queries/useEquipmentAvailableTimeData";
import { EquipmentForm } from "./equipment-form";

interface EquipmentDropdownsProps {
  orgId: string;
  startTime: string;
  setStartTime: (value: string) => void;
  duration: string;
  setDuration: (value: string) => void;
  peopleCount: string;
  setPeopleCount: (value: string) => void;
  durations?: Option[] | null;
  attendingPersons?: Option[] | null;
  equipmentId: number;
  date: string;
}

export function EquipmentDropdowns({
  date,
  orgId,
  durations,
  equipmentId,
  attendingPersons,
}: EquipmentDropdownsProps) {
  const { data: sessionData } = useSession();
  const { data: availableTimeSlots = [], isLoading } =
    useEquipmentAvailableTime(equipmentId, date);

  const dateIsPast = isDateInPast(date);

  const allAllowReservationsFalse = availableTimeSlots.every(
    slot => slot.allow_reservations === false
  );

  if (dateIsPast) {
    return (
      <div className='text-center text-sm  pl-2  bg-[#efefef] py-2 text-[#8a8a8a] rounded-sm'>
        Past date - reservation closed
      </div>
    );
  }

  if (!sessionData) {
    return <LoginToReserve spotsAvailable={1} orgId={orgId} data={null} />;
  }

  if (isLoading) {
    return (
      <>
        <div className='grid grid-cols-2 md:flex md:items-start gap-3'>
          <div className='flex flex-col gap-2 col-span-2 md:flex-1'>
            <Skeleton className='h-4 w-20' />
            <Skeleton className='h-10 w-full' />
          </div>
          <div className='flex flex-col gap-2'>
            <Skeleton className='h-4 w-16' />
            <Skeleton className='h-10 w-full' />
          </div>
          <div className='flex flex-col gap-2'>
            <Skeleton className='h-4 w-24' />
            <Skeleton className='h-10 w-full' />
          </div>
        </div>
        <Skeleton className='h-9 w-full mt-3' />
      </>
    );
  }

  if (
    !isLoading &&
    (!availableTimeSlots ||
      availableTimeSlots.length === 0 ||
      allAllowReservationsFalse)
  ) {
    return (
      <div className='text-center text-sm  pl-2  bg-gray-100 py-2 text-gray-500 rounded-sm'>
        Equipment is not available
      </div>
    );
  }

  return (
    <EquipmentForm
      date={date}
      equipmentId={equipmentId}
      availableTimeSlots={availableTimeSlots}
      durations={durations ?? []}
      attendingPersons={attendingPersons ?? []}
    />
  );
}
