import { useStoreValue } from "@/app/StoreContext";
import { FormSelect } from "@/components/custom/form-select";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Option } from "@/components/ui/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { Calendar } from "lucide-react";
import { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useReserveEquipment } from "../mutations/useReserveEquipment";
import { EquipmentAvailableTime } from "../types";

export const EquipmentReserveSchema = z.object({
  time: z.object(
    {
      value: z.string(),
      label: z.string(),
    },
    { required_error: "Time is required" }
  ),
  duration: z.object(
    {
      value: z.number(),
      label: z.string(),
    },
    { required_error: "Duration is required" }
  ),
  attending_persons: z.object(
    {
      value: z.number(),
      label: z.string(),
    },
    { required_error: "Persons attending is/are required" }
  ),
});

export type EquipmentFormType = z.infer<typeof EquipmentReserveSchema>;

export function EquipmentForm({
  date,
  durations,
  equipmentId,
  availableTimeSlots,
  attendingPersons,
}: {
  date: string;
  equipmentId: number;
  durations: Option[];
  attendingPersons: Option[];
  availableTimeSlots: EquipmentAvailableTime[];
}) {
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const [selectedDuration, setSelectedDuration] = useState<string | null>(null);
  const [selectedAttendingPersons, setSelectedAttendingPersons] = useState<
    string | null
  >(null);

  const form = useForm<EquipmentFormType>({
    resolver: zodResolver(EquipmentReserveSchema),
  });

  const startTimeOptions = availableTimeSlots?.map(slot => ({
    value: slot?.time_value,
    label: `${slot.time_label} - (${slot?.remarks})`,
  }));

  const durationOptions = durations.map(duration => ({
    value: duration.value?.toString() ?? "",
    label: duration.label?.toLowerCase() ?? "",
  }));

  const attendingPersonsOptions = attendingPersons.map(person => ({
    value: person.value?.toString() ?? "",
    label: person.label?.toLowerCase() ?? "",
  }));

  const isFormValid = useMemo(() => {
    return selectedTime && selectedDuration && selectedAttendingPersons;
  }, [selectedTime, selectedDuration, selectedAttendingPersons]);

  const { dispatch } = useStoreValue();
  const {
    mutate: doReservation,
    isPending,
    error,
  } = useReserveEquipment((data?: unknown) => {
    dispatch(() => ({
      reservation: {
        date,
        equipment_id: equipmentId,
        type: "equipment",
        time: selectedTime,
        duration: selectedDuration,
        attending_persons: selectedAttendingPersons,
        ...(data as Record<string, unknown>),
      },
    }));
  });

  const handleReservation = () => {
    doReservation({
      date,
      equipment_id: equipmentId,
      type: "equipment",
      time: selectedTime,
      duration: selectedDuration,
      attending_persons: selectedAttendingPersons,
    });
  };

  return (
    <Form {...form}>
      <div className='grid grid-cols-2 md:flex md:items-start gap-3'>
        <FormSelect
          wrapperClassName='flex-col gap-2 text-sm flex-1'
          name='time'
          placeholder='Select'
          options={startTimeOptions}
          className='w-full text-sm'
          label='Start Time'
          selectWidth='-'
          formItemClassName='flex-1'
          onChange={({ value }) => {
            setSelectedTime(value as string);
          }}
        />

        <FormSelect
          wrapperClassName='flex-col gap-2 text-sm'
          name='duration'
          placeholder='Select'
          options={durationOptions}
          className='w-full'
          label='Duration'
          selectWidth='-'
          onChange={({ value }) => {
            setSelectedDuration(value as string);
          }}
        />

        <FormSelect
          wrapperClassName='flex-col gap-2 text-sm'
          name='attending_persons'
          placeholder='Select'
          options={attendingPersonsOptions}
          className='w-full'
          label='People Attending'
          selectWidth='-'
          onChange={({ value }) => {
            setSelectedAttendingPersons(value as string);
          }}
        />
      </div>

      <Button
        variant='outline'
        className='w-full text-blue-500 text-xs font-bold whitespace-nowrap border-blue-500'
        onClick={handleReservation}
        disabled={!isFormValid || isPending}
      >
        <span className='hidden lg:flex md:flex'>
          <Calendar size={16} />
        </span>

        <span className='lg:pl-1 md:pl-1 text-[10px] sm:text-xs'>Reserve</span>
      </Button>
      {error && (
        <p className='text-red-500 text-xs text-center'>{error.message}</p>
      )}
    </Form>
  );
}
