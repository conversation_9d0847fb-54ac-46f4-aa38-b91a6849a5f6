import { Calendar } from "@/components/custom/icons/calendar";
import { Card, CardContent } from "@/components/ui/card";
import { format, parseISO } from "date-fns";

interface DateCardProps {
  selectedDate?: string;
  className?: string;
}

export default function DateCard({
  selectedDate,
  className = "",
}: DateCardProps) {
  return (
    <Card className={`h-[17] rounded w-fit shadow-none ${className}`}>
      <CardContent className='p-3 md:p-4'>
        <div className='flex gap-2 items-center'>
          <Calendar className='text-gray-500' />
          <p className='text-sm md:text-sm'>
            {format(
              selectedDate ? parseISO(selectedDate) : new Date().toISOString(),
              "EEE, MMM d"
            )}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
