"use client";

import { BASE_API_URL_CLIENT } from "@/app/classes/[orgId]/actions/constant";
import { queryOptions, useQuery } from "@tanstack/react-query";
import { EquipmentWeeklyType } from "../types";

type FetchWeeklyEquipmentParams = {
  orgId: string;
  params: Record<string, string | number>;
  xSource?: string;
  token?: string;
};

export const fetchWeeklyEquipment = async ({
  orgId,
  params,
  xSource,
  token,
}: FetchWeeklyEquipmentParams): Promise<EquipmentWeeklyType> => {
  try {
    const headers: {
      "X-SOURCE": string;
      authorization?: string;
    } = {
      "X-SOURCE": xSource ?? "SCHEDULE",
    };

    const urlParams = new URLSearchParams({
      ...params,
      university_id: orgId,
    }).toString();

    let url = `${BASE_API_URL_CLIENT}/equipment/schedules/unauth?${urlParams}&exclude_if_closed_or_cancelled=true&exclude_past_classes=true`;

    if (token) {
      url = `${BASE_API_URL_CLIENT}/equipment/schedules?${urlParams}&exclude_if_closed_or_cancelled=true&exclude_past_classes=true`;
      headers.authorization = `Bearer ${token}`;
    }

    const response = await fetch(url, { headers: headers });
    return await response.json();
  } catch (err) {
    throw new Error("Sorry, there was an error fetching the data.");
  }
};

export const equipmentQueryOptions = (
  orgId: string,
  searchParams: Record<string, string | number>
) =>
  queryOptions({
    queryKey: [orgId, searchParams],
  });

type EquipmentWeeklyDataParams = {
  orgId: string;
  searchParams: Record<string, string | number>;
  enabledRequest?: boolean;
  xSource?: string;
  token?: string;
};

export const useEquipmentWeeklyData = ({
  orgId,
  searchParams,
  enabledRequest,
  xSource,
  token,
}: EquipmentWeeklyDataParams) => {
  return useQuery({
    queryKey: queryOptions({
      queryKey: [orgId, searchParams, token],
    }).queryKey,
    enabled: enabledRequest,
    refetchOnWindowFocus: false,
    refetchInterval: false,
    refetchOnMount: true,
    select: data => data?.schedule,
    queryFn: () =>
      fetchWeeklyEquipment({ orgId, params: searchParams, xSource, token }),
  });
};
