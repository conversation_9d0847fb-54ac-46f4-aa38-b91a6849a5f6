"use client";

import { queryOptions, useQuery } from "@tanstack/react-query";
import { fetchEquipmentByOrgId } from "../actions/fetchEquipmentByOrgId";
import { useSession } from "@/components/custom/login-auth/auth-provider";

export const equipmentQueryOptions = (
  orgId: string,
  searchParams: Record<string, string | number>,
  token?: string | null
) =>
  queryOptions({
    queryKey: [orgId, searchParams, token],
  });

export const useEquipmentData = (
  orgId: string,
  searchParams: Record<string, string | number>
) => {
  const { data: sessionData } = useSession();
  const response = useQuery({
    queryKey: equipmentQueryOptions(orgId, searchParams, sessionData?.token)
      .queryKey,
    queryFn: async ({ signal }) =>
      fetchEquipmentByOrgId(orgId, searchParams, signal),
    staleTime: 30000,
    refetchInterval: 30 * 1000,
    refetchIntervalInBackground: true,
  });

  return response;
};
