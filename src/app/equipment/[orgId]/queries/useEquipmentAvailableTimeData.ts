"use client";

import { BASE_API_URL_CLIENT } from "@/app/classes/[orgId]/actions/constant";
import { getSession } from "@/components/custom/login-auth/auth-provider";
import { queryOptions, useQuery } from "@tanstack/react-query";
import { EquipmentAvailableTime } from "../types";

export const fetchEquipmentAvailableTime = async (
  equipmentId: number,
  date: string,
  signal?: AbortSignal
): Promise<EquipmentAvailableTime[]> => {
  const session = await getSession();
  const headers: { "Content-Type": string; authorization?: string } = {
    "Content-Type": "application/json",
  };

  headers.authorization = `Bearer ${session?.token}`;

  try {
    const responseJson = await fetch(
      `${BASE_API_URL_CLIENT}/equipment/${equipmentId}/available_times?date=${date}`,
      { headers, signal }
    );
    const response = await responseJson.json();

    return response.data;
  } catch (err) {
    throw new Error("Sorry, there was an error fetching the data.");
  }
};

export const equipmentQueryOptions = (equipmentId: string, date: string) =>
  queryOptions({
    queryKey: [equipmentId, date],
  });

export const useEquipmentAvailableTime = (
  equipmentId: number,
  date: string
) => {
  return useQuery({
    queryKey: queryOptions({
      queryKey: [equipmentId, date],
    }).queryKey,
    refetchOnWindowFocus: false,
    refetchInterval: false,
    queryFn: ({ signal }) =>
      fetchEquipmentAvailableTime(equipmentId, date, signal),
  });
};
