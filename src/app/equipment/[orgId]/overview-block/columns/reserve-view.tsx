import { obtainDateFrame } from "@/app/classes/[orgId]/utils";
import { CancelButton } from "@/components/custom/cancel-button";
import { formatReservationDate } from "@/components/custom/reservations/helper-components";
import { isMobileOnly } from "react-device-detect";

type ReservationViewType = {
  attendingPerson: string;
  startTime: string;
  endTime: string;
  id?: number;
  name?: string;
};

const DesktopReserveView = ({
  attendingPerson,
  startTime,
  endTime,
  name,
}: ReservationViewType) => {
  return (
    <div>
      <p className='font-bold mb-3'>{name}</p>
      <p className='font-bold'>
        {`You have a reservation for
  ${attendingPerson} person(s) at ${formatReservationDate(startTime)} from ${obtainDateFrame(startTime, endTime)} `}
      </p>
    </div>
  );
};

const MobileReserveView = ({
  startTime,
  endTime,
  attendingPerson,
  id,
  name,
}: ReservationViewType) => {
  return (
    <div className='flex flex-col  gap-4'>
      <div className='whitespace-nowrap'>
        <div className='flex justify-between'>
          <p>Name:</p>
          <p className='font-bold pl-2'>{name}</p>
        </div>
        <div className='flex justify-between'>
          <p>Date:</p>
          <p className='font-bold pl-2'>{formatReservationDate(startTime)}</p>
        </div>
        <div className='flex justify-between'>
          <p>Person attending:</p>
          <p className='font-bold pl-2'>{attendingPerson}</p>
        </div>
        <div className='flex justify-between'>
          <p>At:</p>
          <p className='font-bold pl-2'>
            {obtainDateFrame(startTime, endTime)}
          </p>
        </div>
      </div>
      <CancelButton id={Number(id)} />
    </div>
  );
};

export const ReserVationView = ({
  startTime,
  endTime,
  attendingPerson,
  id,
  name,
}: ReservationViewType) => {
  return isMobileOnly ? (
    <MobileReserveView
      startTime={startTime}
      endTime={endTime}
      attendingPerson={attendingPerson}
      id={id}
      name={name}
    />
  ) : (
    <DesktopReserveView
      startTime={startTime}
      endTime={endTime}
      attendingPerson={attendingPerson}
      name={name}
    />
  );
};
