"use client";

import { DataTable } from "@/components/data-table/data-table";

import { useDesktopColumns } from "./columns/useDesktopColumns";
import { useEquipmentData } from "../queries/useEquipmentData";
import { useParams } from "next/navigation";
import { isMobileOnly } from "react-device-detect";
import { useMobileColumns } from "./columns/useMobileColumns";
import { Form } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { EquipmentReserveSchema } from "./columns/location";
import { zodResolver } from "@hookform/resolvers/zod";
import { ReserveSuccessModal } from "@/app/classes/[orgId]/modules/overview-block/reserve-success-modal";
import { useStoreValue } from "@/app/StoreContext";
import { Fragment, memo, useMemo } from "react";
import { isEmpty } from "lodash/fp";

export const EquipmentOverview = memo(
  ({ searchParams }: { searchParams: Record<string, string | number> }) => {
    const { orgId } = useParams();

    const { isPending, data } = useEquipmentData(String(orgId), searchParams);

    const mobileColumns = useMobileColumns();
    const desktopColumns = useDesktopColumns();

    const columns = isMobileOnly ? mobileColumns : desktopColumns;

    const form = useForm<z.infer<typeof EquipmentReserveSchema>>({
      resolver: zodResolver(EquipmentReserveSchema),
    });

    const { state: reservations, dispatch } = useStoreValue(
      state => state.reservation
    );

    const updatedReservation = useMemo(() => {
      const found = data?.find(item => item.id === reservations?.id);

      if (found?.current_user_reservation) {
        return {
          ...found,
          ...found?.current_user_reservation,
          name: `${found?.name} - ${found?.gym_name}`,
          start_time: found?.current_user_reservation?.start_time
            ?.toString()
            .split(" ")[1],
          end_time: found?.current_user_reservation?.end_time
            ?.toString()
            .split(" ")[1],
        };
      }
      return null;
    }, [data, reservations]);

    return (
      <Fragment>
        <Form {...form}>
          <div className='container rounded-lg border-2  p-8 pb-24'>
            <DataTable
              classNames='h-max-content pb-16'
              shouldShowHover={false}
              data={data || []}
              columns={columns}
              isLoading={isPending}
              searchPlaceholder='Search by location or next available'
              emptyState='There are no data available. Click on another date to see more data.'
            />
          </div>
        </Form>

        {!isEmpty(updatedReservation) && (
          <ReserveSuccessModal
            reservations={updatedReservation}
            onClose={() => dispatch(() => ({ reservation: null }))}
            paramDate={searchParams?.date as string}
            embedType='equipment'
          />
        )}
      </Fragment>
    );
  }
);
