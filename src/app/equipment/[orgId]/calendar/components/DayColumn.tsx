import { DAYS_OF_WEEK, formatDate } from "@/common/common.utils";
import { format } from "date-fns";
import { memo } from "react";
import { styled } from "styled-components";
import tw from "twin.macro";
import { EquipmentSchedule, SelectedEquipmentSchedule } from "../../types";
import { EquipmentCard } from "./EquipmentCard";

const StyledHeader = styled.div<{ color?: string }>`
  ${tw`font-bold p-2 text-white`};
  background-color: ${({ color }) => color};
`;

interface DayColumnProps {
  day: (typeof DAYS_OF_WEEK)[number];
  date: Date;
  schedules: EquipmentSchedule[];
  color?: string;
  setSelected: React.Dispatch<
    React.SetStateAction<SelectedEquipmentSchedule | undefined>
  >;
}

export const DayColumn = memo(
  ({ day, date, schedules, color, setSelected }: DayColumnProps) => {
    return (
      <div className='border overflow-hidden bg-white shadow cursor-pointer hover:bg-gray-100'>
        <StyledHeader color={color} className='flex justify-between'>
          <span>{day.label}</span>
          <span>{format(date, "d")}</span>
        </StyledHeader>
        <div className='p-2 space-y-2'>
          {!schedules?.length && <span className='text-sm' />}

          {schedules?.map(schedule => (
            <div key={`${schedule.equipment.id}-${date.toISOString()}`}>
              <EquipmentCard
                schedule={schedule}
                setSelected={setSelected}
                color={color}
                selectedDate={formatDate(date)}
              />
            </div>
          ))}
        </div>
      </div>
    );
  }
);

DayColumn.displayName = "DayColumn";
