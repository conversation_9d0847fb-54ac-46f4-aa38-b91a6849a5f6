import { obtainDateFrame } from "@/app/classes/[orgId]/utils";
import * as Tooltip from "@radix-ui/react-tooltip";
import { memo } from "react";
import { EquipmentSchedule } from "../../types";

export const BlockScheduleItemWithStrikethrough = memo(
  ({
    blockSchedule,
  }: {
    blockSchedule: NonNullable<EquipmentSchedule["block_schedules"]>[number];
  }) => {
    return (
      <Tooltip.Provider delayDuration={200}>
        <Tooltip.Root>
          <Tooltip.Trigger asChild>
            <div className='flex items-center gap-2'>
              <span className='text-sm text-gray-500 relative'>
                <span className='relative inline-block'>
                  {obtainDateFrame(
                    blockSchedule.start_time,
                    blockSchedule.end_time
                  )}
                  <span className='absolute left-0 right-0 top-1/2 border-t-2 border-black' />
                </span>
              </span>
            </div>
          </Tooltip.Trigger>
          <Tooltip.Portal>
            <Tooltip.Content
              className='bg-black max-w-xs leading-6 text-white rounded-md px-4 py-2.5 text-sm select-none data-[state=delayed-open]:animate-in data-[state=delayed-open]:fade-in-0 data-[state=delayed-open]:zoom-in-95'
              side='top'
              align='center'
              sideOffset={5}
            >
              <span className='block'>{`Times not available due to ${blockSchedule.category}`}</span>
              <span className='text-gray-300'>{blockSchedule.message}</span>
              <Tooltip.Arrow className='fill-border' />
            </Tooltip.Content>
          </Tooltip.Portal>
        </Tooltip.Root>
      </Tooltip.Provider>
    );
  }
);

BlockScheduleItemWithStrikethrough.displayName =
  "BlockScheduleItemWithStrikethrough";
