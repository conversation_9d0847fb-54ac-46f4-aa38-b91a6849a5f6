import { formatTimeToHHMM, obtainDateFrame } from "@/app/classes/[orgId]/utils";
import * as Tooltip from "@radix-ui/react-tooltip";
import { memo } from "react";
import { FaRegCalendarCheck } from "react-icons/fa";
import { EquipmentSchedule } from "../../types";

export const ReservedEquipment = memo(
  ({
    reservedSchedule,
    equipmentName,
  }: {
    reservedSchedule: NonNullable<
      EquipmentSchedule["current_user_reservations"]
    >[number];
    equipmentName: string;
  }) => {
    return (
      <Tooltip.Provider delayDuration={200}>
        <Tooltip.Root>
          <Tooltip.Trigger asChild>
            <div className='flex items-center gap-2 font-bold text-[#0C50B6]'>
              <FaRegCalendarCheck />
              <span className='text-sm  relative'>
                <span>
                  {obtainDateFrame(
                    reservedSchedule.start_time,
                    reservedSchedule.end_time
                  )}
                </span>
              </span>
            </div>
          </Tooltip.Trigger>
          <Tooltip.Portal>
            <Tooltip.Content
              className='bg-black max-w-xs leading-6 text-white rounded-md px-4 py-2.5 text-sm select-none data-[state=delayed-open]:animate-in data-[state=delayed-open]:fade-in-0 data-[state=delayed-open]:zoom-in-95'
              side='top'
              align='center'
              sideOffset={5}
            >
              <span className='block'>
                You&apos;ve reserved this equipment {equipmentName}
              </span>
              <span className='text-gray-300'>
                from {formatTimeToHHMM(reservedSchedule.start_time)} to{" "}
                {formatTimeToHHMM(reservedSchedule.end_time)}
              </span>
              <Tooltip.Arrow className='fill-border' />
            </Tooltip.Content>
          </Tooltip.Portal>
        </Tooltip.Root>
      </Tooltip.Provider>
    );
  }
);

ReservedEquipment.displayName = "ReservedEquipment";
