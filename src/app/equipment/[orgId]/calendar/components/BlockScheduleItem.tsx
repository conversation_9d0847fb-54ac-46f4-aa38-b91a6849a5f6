import { formatTimeToHHMM } from "@/app/classes/[orgId]/utils";
import { memo } from "react";
import { IoCloseCircleOutline } from "react-icons/io5";
import { EquipmentSchedule } from "../../types";

export const BlockScheduleItem = memo(
  ({
    blockSchedule,
  }: {
    blockSchedule: NonNullable<EquipmentSchedule["block_schedules"]>[number];
  }) => {
    return (
      <span className='flex text-sm text-red-500 gap-2 items-start'>
        <IoCloseCircleOutline size={22} />
        <span className='block flex-1 break-words whitespace-normal max-w-[120px]'>
          Not available from {formatTimeToHHMM(blockSchedule.start_time)} to{" "}
          {formatTimeToHHMM(blockSchedule.end_time)} due to{" "}
          {blockSchedule.category}
        </span>
      </span>
    );
  }
);

BlockScheduleItem.displayName = "BlockScheduleItem";
