import { obtainDateFrame } from "@/app/classes/[orgId]/utils";
import { isDateInPast } from "@/common/common.utils";
import { Plus } from "lucide-react";
import Link from "next/link";
import { memo, useCallback } from "react";
import { EquipmentSchedule, SelectedEquipmentSchedule } from "../../types";
import { BlockScheduleItem } from "./BlockScheduleItem";
import { ReservedEquipment } from "./ReservedEquipment";

type EquipmentCardProps = {
  schedule: EquipmentSchedule;
  selectedDate: string;
  color?: string;
  setSelected: React.Dispatch<
    React.SetStateAction<SelectedEquipmentSchedule | undefined>
  >;
};

export const EquipmentCard = memo(
  ({ schedule, selectedDate, color, setSelected }: EquipmentCardProps) => {
    const {
      equipment: { name: equipmentName, id: equipmentId, facility },
      slots,
      block_schedules: blockSchedules,
      current_user_reservations,
    } = schedule;

    const handleClick = useCallback(() => {
      setSelected({
        ...schedule,
        selectedDate,
      });
    }, [schedule, setSelected, selectedDate]);

    const isPast = isDateInPast(selectedDate);
    const isFull = slots?.length === 0;

    return (
      <Link
        href={""}
        className={`bg-white border rounded-sm cursor-pointer border-gray-200 shadow-sm gap-0 flex flex-col ${selectedDate ? "hover:bg-gray-100" : ""}`}
        onClick={handleClick}
      >
        <div>
          <div className='text-sm p-2 pb-0 flex justify-between items-center'>
            <span className='font-semibold'>{equipmentName}</span>
            {!isPast && !isFull && (
              <Plus
                onClick={() => {}}
                style={{ color }}
                className='h-4 w-4 cursor-pointer'
              />
            )}
          </div>
          <div className='px-2 pt-1 text-xs uppercase text-gray-500'>
            {facility}
          </div>
          <div className='p-2 pt-3 flex-grow flex flex-col justify-between gap-2'>
            {!!slots?.length && (
              <div className='flex flex-col gap-1'>
                <>
                  {slots?.map(slot => (
                    <div key={`${equipmentId}-${slot.id}-${slot.start_time}`}>
                      <span className='text-sm'>
                        {obtainDateFrame(slot.start_time, slot.end_time)}
                      </span>
                    </div>
                  ))}
                </>
              </div>
            )}

            {!!current_user_reservations?.length && (
              <div className='flex flex-col gap-1'>
                <>
                  {current_user_reservations?.map(reservedSchedule => (
                    <div
                      key={`${equipmentId}-${reservedSchedule.reservation_id}-${reservedSchedule.start_time}`}
                    >
                      <ReservedEquipment
                        equipmentName={equipmentName}
                        reservedSchedule={reservedSchedule}
                      />
                    </div>
                  ))}
                </>
              </div>
            )}

            {!!blockSchedules?.length && (
              <div className='flex flex-col gap-1'>
                <>
                  {blockSchedules?.map(blockSchedule => (
                    <div
                      key={`${equipmentId}-${blockSchedule.block_schedule_id}-${blockSchedule.start_time}`}
                    >
                      <BlockScheduleItem blockSchedule={blockSchedule} />
                    </div>
                  ))}
                </>
              </div>
            )}

            {/* {current_user_reservations?.map(reservedSchedule => (
              <div
                className='flex flex-col gap-2'
                key={`${equipmentId}-${reservedSchedule.reservation_id}-${reservedSchedule.start_time}`}
              >
                <ReservedEquipment
                  equipmentName={equipmentName}
                  reservedSchedule={reservedSchedule}
                />
              </div>
            ))}

            {blockSchedules?.map(blockSchedule => (
              <div
                className='flex flex-col gap-2'
                key={`${equipmentId}-${blockSchedule.block_schedule_id}-${blockSchedule.start_time}`}
              >
                <BlockScheduleItem blockSchedule={blockSchedule} />
              </div>
            ))} */}
          </div>
        </div>
      </Link>
    );
  }
);

EquipmentCard.displayName = "EquipmentCard";
