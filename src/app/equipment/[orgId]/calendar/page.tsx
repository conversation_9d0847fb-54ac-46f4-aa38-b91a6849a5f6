import { EmbedPageViewTracker } from "@/app/classes/[orgId]/modules/analytics/EmbedPageViewTracker";
import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { ActivityFilters } from "@/components/custom/activity-filters";
import { Footer } from "@/components/custom/footer";
import { LoginModal } from "@/components/custom/login-auth/login-modal";
import { LoginLogoutButton } from "@/components/custom/login-logout-button";
import { ReservationsModal } from "@/components/custom/reservations/reservations-modal";
import { WeekDatePicker } from "@/components/custom/week-date-picker";
import { EquipmentDownloadPDF } from "@/components/pdf-downloader-module/equipment-pdf-modal";
import { FirebaseEvent } from "@/lib/firebase/config";
import { EquipmentScheduleOverview } from "./schedule";

export default async function Schedule({
  params,
  searchParams,
}: Readonly<{
  params: {
    orgId: string;
  };
  searchParams: Record<string, string | number>;
}>) {
  const {
    clientName,
    configs,
    appUrls,
    facilitiesOptions,
    clientLogo,
    clientPdfHeader,
    equipmentCategoriesOptions,
  } = await fetchClientConfigs(params.orgId);

  const hideFilters = searchParams["hide-filters"] === "true";

  return (
    <>
      <EmbedPageViewTracker
        pageTitle='Equipment Schedule Page'
        event={FirebaseEvent.SCHEDULE_VIEW_OPENED}
      />
      <div className='overflow-hidden mb-20 container'>
        <div
          className={`container rounded-b-none rounded-t-lg border-2 border-white-300 !bg-white flex flex-col mt-4 ${
            hideFilters ? "mb-6" : "mb-0"
          }`}
        >
          <LoginLogoutButton ctaLabel='Login to reserve' />
          <WeekDatePicker />
        </div>
        {!Number(hideFilters) && (
          <ActivityFilters
            facilitiesOptions={facilitiesOptions}
            categoriesOptions={equipmentCategoriesOptions}
            activityName='equipment_type_id'
            activityLabel=''
            filterByTimes={false}
          />
        )}

        <EquipmentScheduleOverview
          orgId={params.orgId}
          searchParams={searchParams}
        />
        <ReservationsModal />
        <EquipmentDownloadPDF
          facilitiesOptions={facilitiesOptions}
          categoriesOptions={equipmentCategoriesOptions}
          clientName={clientName}
          clientId={params.orgId}
          clientLogo={clientLogo}
          clientPdfHeader={clientPdfHeader}
          appUrls={appUrls}
        />
        <Footer
          appUrls={appUrls}
          infoText='Make reservations today on the go! Download the app today:'
        />
      </div>
      <LoginModal
        buttonColor={configs?.embed?.button_color}
        appUrls={appUrls}
        configs={configs}
        orgId={params.orgId}
      />
    </>
  );
}
