import { useStoreValue } from "@/app/StoreContext";
import { DAYS_OF_WEEK } from "@/common/common.utils";
import { addDays } from "date-fns";
import { memo } from "react";
import { EquipmentSchedule, SelectedEquipmentSchedule } from "../types";
import { DayColumn } from "./components/DayColumn";
import { groupEquipmentSchedulesByDay } from "./utils";

interface ClassCalendarProps {
  schedules: EquipmentSchedule[];
  weekStart: Date;
  setSelected: React.Dispatch<
    React.SetStateAction<SelectedEquipmentSchedule | undefined>
  >;
  selected: SelectedEquipmentSchedule | undefined;
}

export const EquipmentCalendar = memo(
  ({ weekStart, schedules, setSelected }: ClassCalendarProps) => {
    const { state: configs } = useStoreValue(store => store.configs);
    const groupedEquipment = groupEquipmentSchedulesByDay(schedules);

    return (
      <div className='relative z-0'>
        <div className='grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-7 gap-2 z-0 items-stretch'>
          {DAYS_OF_WEEK.map((day, index) => {
            const date = addDays(weekStart, index);

            return (
              <div key={`${day.value}-${date.toISOString()}`}>
                <DayColumn
                  day={day}
                  date={date}
                  schedules={groupedEquipment[day.value] ?? []}
                  color={configs?.accent_color}
                  setSelected={setSelected}
                />
              </div>
            );
          })}
        </div>
        {!schedules?.length && (
          <p className='text-center pt-4 font-semibold'>
            Sorry there are no equipment schedules available, Please update your
            filters
          </p>
        )}
      </div>
    );
  }
);

EquipmentCalendar.displayName = "EquipmentCalendar";
