"use client";

import { ReserveSuccessModal } from "@/app/classes/[orgId]/modules/overview-block/reserve-success-modal";
import { obtainDateFrame } from "@/app/classes/[orgId]/utils";
import { useStoreValue } from "@/app/StoreContext";
import { useWeekDates } from "@/common/hooks/useWeekDates";
import { ALL_OPTION } from "@/components/custom/activity-filters";
import { CalendarLoader } from "@/components/custom/calendar-loader";
import { useSession } from "@/components/custom/login-auth/auth-provider";
import { TableFilters } from "@/components/data-table/table-filters";
import { useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { matchSorter } from "match-sorter";
import { useEffect, useMemo, useState } from "react";
import { useEquipmentWeeklyData } from "../queries/useWeeklyEquipmentData";
import { SelectedEquipmentSchedule } from "../types";
import { EquipmentCalendar } from "./calendar";
import { EquipmentModal } from "./equipment-modal";

export const EquipmentScheduleOverview = ({
  orgId,
  searchParams,
}: {
  orgId: string;
  searchParams: Record<string, string | number>;
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const { weekStart, weekEnd } = useWeekDates();
  const [selected, setSelected] = useState<
    SelectedEquipmentSchedule | undefined
  >(undefined);

  const { data: sessionData } = useSession();
  const queryClient = useQueryClient();

  const { state: reservation, dispatch } = useStoreValue(
    state => state.reservation
  );

  const { equipment_type_id, gym_id, class_start_time, class_end_time } =
    searchParams;

  const queryParams = useMemo(
    () => ({
      facility_id: gym_id === ALL_OPTION ? "" : gym_id ?? "",
      equipment_type_id: equipment_type_id ?? "",
      equipment_start_time: class_start_time ?? "",
      equipment_end_time: class_end_time ?? "",
      start_date: format(weekStart, "yyyy-MM-dd"),
      end_date: format(weekEnd, "yyyy-MM-dd"),
    }),
    [
      gym_id,
      equipment_type_id,
      class_start_time,
      class_end_time,
      weekStart,
      weekEnd,
    ]
  );

  const { data: schedules, isPending } = useEquipmentWeeklyData({
    orgId,
    searchParams: queryParams,
    enabledRequest: true,
    token: sessionData?.token,
  });

  const filteredSchedules = searchTerm
    ? matchSorter(schedules ?? [], searchTerm, {
        keys: [
          "equipment.name",
          "equipment.facility",
          "equipment.durations.label",
          item =>
            item.slots?.map(slot =>
              obtainDateFrame(slot.start_time, slot.end_time)
            ) ?? [],
        ],
      })
    : schedules ?? [];

  useEffect(() => {
    queryClient.invalidateQueries({
      queryKey: [orgId, queryParams, sessionData?.token],
      refetchType: "active",
    });
  }, [sessionData?.token, orgId, queryParams, queryClient]);

  return (
    <>
      <div className='relative z-0 mb-4'>
        <TableFilters
          placeholder='Search by equipment name, facility, time or duration'
          searchTerm={searchTerm}
          onSearchedClasses={setSearchTerm}
        />

        {isPending ? (
          <CalendarLoader />
        ) : (
          <EquipmentCalendar
            schedules={filteredSchedules}
            weekStart={weekStart}
            setSelected={setSelected}
            selected={selected}
          />
        )}
      </div>

      {selected && (
        <EquipmentModal
          orgId={orgId}
          selected={selected}
          isOpen={Boolean(selected)}
          setIsOpen={() => setSelected(undefined)}
        />
      )}

      {reservation && (
        <ReserveSuccessModal
          reservations={reservation}
          onClose={() => {
            setSelected(undefined);
            dispatch(() => ({ reservation: null }));
          }}
          paramDate={reservation?.date as string}
          embedType='equipment'
        />
      )}
    </>
  );
};
