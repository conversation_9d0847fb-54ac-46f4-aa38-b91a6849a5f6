"use client";
import { useSession } from "@/components/custom/login-auth/auth-provider";
import {
  LoginForm,
  LoginSchema,
} from "@/components/custom/login-auth/login-form";
import { z } from "zod";
import { SelectedEquipmentSchedule } from "../types";

export const LoginToReserve = ({
  orgId,
}: {
  spotsAvailable?: number;
  orgId?: string;
  data: SelectedEquipmentSchedule | null;
}) => {
  const { signIn, isError, errorMessage } = useSession();

  const handleSubmit = async (fieldValue: z.infer<typeof LoginSchema>) => {
    try {
      signIn({
        email: fieldValue.email,
        password: fieldValue.password,
        orgId,
      });
    } catch (error) {
      throw new Error("Something bad happened, try again");
    }
  };

  return (
    <div className='mb-5'>
      <LoginForm
        handleSubmit={handleSubmit}
        isError={isError}
        placeholder='Login to Reserve'
        isLoading={false}
        className='lg:w-full'
        errorMessage={errorMessage}
        title='Login to Make a Reservation'
      />
    </div>
  );
};
