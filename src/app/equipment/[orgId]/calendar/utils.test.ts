import { format } from "date-fns";
import {
  BlockSchedule,
  EquipmentSchedule,
  EquipmentSlot,
  EquipmentType,
  ReservedSchedule,
} from "../types";
import {
  groupEquipmentSchedulesByDay,
  groupEquipmentSlotsByStartTime,
} from "./utils";

const createMockEquipment = (id: number, name: string): EquipmentType => ({
  id,
  name,
  facility: "Test Facility",
  type: "Test Type",
  image_url: "",
  durations: [{ value: 30, label: "30 mins" }],
  attending_persons: [{ value: 1, label: "1 Person" }],
});

const createMockSlot = (
  id: number,
  startTime: string,
  endTime: string,
  dayOfWeek: string
): EquipmentSlot => ({
  id,
  start_time: startTime,
  end_time: endTime,
  day_of_week: dayOfWeek,
});

const createMockBlockSchedule = (
  id: number,
  startTime: string,
  endTime: string,
  dayOfWeek: string,
  category: string = "Test Category",
  message: string = "Test Message"
): BlockSchedule => ({
  id,
  block_schedule_id: id.toString(),
  equipment_id: id.toString(),
  start_time: startTime,
  end_time: endTime,
  day_of_week: dayOfWeek,
  category,
  message,
});

const createMockReservedSchedule = (
  id: number,
  startDateTime: string,
  endDateTime: string
): ReservedSchedule => {
  const startTime = startDateTime.split(" ")[1] || "00:00";
  const endTime = endDateTime.split(" ")[1] || "00:00";
  let dayOfWeek = "invalid";

  try {
    dayOfWeek = format(new Date(startDateTime), "EEEE").toLowerCase();
  } catch (error) {
    // If date parsing fails, keep the default "invalid" day
  }

  return {
    id,
    reservation_id: id,
    start_date_time: startDateTime,
    end_date_time: endDateTime,
    start_time: startTime,
    end_time: endTime,
    day_of_week: dayOfWeek,
  };
};

describe("Equipment Schedule Utils", () => {
  describe("groupEquipmentSlotsByStartTime", () => {
    it("should group slots by start time", () => {
      const slots = [
        createMockSlot(1, "09:00", "10:00", "Mon"),
        createMockSlot(2, "09:00", "10:30", "Mon"),
        createMockSlot(3, "10:00", "11:00", "Mon"),
      ];

      const result = groupEquipmentSlotsByStartTime(slots);

      expect(Object.keys(result)).toEqual(["09:00", "10:00"]);
      expect(result["09:00"]).toHaveLength(2);
      expect(result["10:00"]).toHaveLength(1);
    });

    it("should handle empty slots array", () => {
      const result = groupEquipmentSlotsByStartTime([]);
      expect(Object.keys(result)).toHaveLength(0);
    });

    it("should handle invalid start times", () => {
      const slots = [
        {
          ...createMockSlot(1, "09:00", "10:00", "Mon"),
          start_time: undefined as any,
        },
      ];
      const result = groupEquipmentSlotsByStartTime(slots);
      expect(Object.keys(result)).toHaveLength(0);
    });
  });

  describe("groupEquipmentSchedulesByDay", () => {
    const mockEquipment1 = createMockEquipment(1, "Equipment 1");
    const mockEquipment2 = createMockEquipment(2, "Equipment 2");

    const mockSchedule: EquipmentSchedule[] = [
      {
        equipment: mockEquipment1,
        slots: [
          createMockSlot(1, "09:00", "10:00", "Mon"),
          createMockSlot(2, "11:00", "12:00", "Tue"),
        ],
        block_schedules: [
          createMockBlockSchedule(
            1,
            "14:00",
            "15:00",
            "Mon",
            "Maintenance",
            "Daily Check"
          ),
          createMockBlockSchedule(
            2,
            "15:00",
            "16:00",
            "Wed",
            "Class",
            "Swimming Class"
          ),
        ],
        current_user_reservations: [
          createMockReservedSchedule(
            1,
            "2024-03-18 13:00:00",
            "2024-03-18 14:00:00"
          ),
          createMockReservedSchedule(
            2,
            "2024-03-19 15:00:00",
            "2024-03-19 16:00:00"
          ),
        ],
      },
      {
        equipment: mockEquipment2,
        slots: [
          createMockSlot(3, "13:00", "14:00", "Mon"),
          createMockSlot(4, "14:00", "15:00", "Tue"),
        ],
        block_schedules: [
          createMockBlockSchedule(
            3,
            "16:00",
            "17:00",
            "Mon",
            "Class",
            "Tennis Class"
          ),
        ],
        current_user_reservations: [
          createMockReservedSchedule(
            3,
            "2024-03-18 16:00:00",
            "2024-03-18 17:00:00"
          ),
        ],
      },
    ];

    it("should handle undefined schedule", () => {
      const result = groupEquipmentSchedulesByDay(undefined);
      expect(Object.keys(result)).toEqual([
        "mon",
        "tue",
        "wed",
        "thu",
        "fri",
        "sat",
        "sun",
      ]);
      Object.values(result).forEach(day => expect(day).toEqual([]));
    });

    it("should handle empty schedule array", () => {
      const result = groupEquipmentSchedulesByDay([]);
      expect(Object.keys(result)).toEqual([
        "mon",
        "tue",
        "wed",
        "thu",
        "fri",
        "sat",
        "sun",
      ]);
      Object.values(result).forEach(day => expect(day).toEqual([]));
    });

    it("should group equipment by day with both slots and block schedules", () => {
      const result = groupEquipmentSchedulesByDay(mockSchedule);

      // Check Monday (has both slots and block schedules)
      expect(result["mon"]).toHaveLength(2);
      const mondayEquip1 = result["mon"]?.find(e => e.equipment.id === 1);
      const mondayEquip2 = result["mon"]?.find(e => e.equipment.id === 2);

      expect(mondayEquip1?.slots).toHaveLength(1);
      expect(mondayEquip1?.block_schedules).toHaveLength(1);
      expect(mondayEquip1?.slots[0]?.start_time).toBe("09:00");
      expect(mondayEquip1?.block_schedules?.[0]?.category).toBe("Maintenance");

      expect(mondayEquip2?.slots).toHaveLength(1);
      expect(mondayEquip2?.block_schedules).toHaveLength(1);
      expect(mondayEquip2?.slots[0]?.start_time).toBe("13:00");
      expect(mondayEquip2?.block_schedules?.[0]?.category).toBe("Class");

      // Check Tuesday (has only slots)
      expect(result["tue"]).toHaveLength(2);
      const tuesdayEquipments = result["tue"] ?? [];
      expect(tuesdayEquipments[0]?.slots).toHaveLength(1);
      expect(tuesdayEquipments[1]?.slots).toHaveLength(1);
      expect(tuesdayEquipments[0]?.block_schedules).toHaveLength(0);
      expect(tuesdayEquipments[1]?.block_schedules).toHaveLength(0);

      // Check Wednesday (has only block schedule)
      expect(result["wed"]).toHaveLength(1);
      const wednesdayEquipment = result["wed"]?.[0];
      expect(wednesdayEquipment?.slots).toHaveLength(0);
      expect(wednesdayEquipment?.block_schedules).toHaveLength(1);
      expect(wednesdayEquipment?.block_schedules?.[0]?.category).toBe("Class");

      // Check empty days
      ["thu", "fri", "sat", "sun"].forEach(day => {
        expect(result[day]).toHaveLength(0);
      });
    });

    it("should handle equipment with no slots and no block schedules", () => {
      const emptySchedule: EquipmentSchedule[] = [
        {
          equipment: mockEquipment1,
          slots: [],
          block_schedules: [],
        },
      ];
      const result = groupEquipmentSchedulesByDay(emptySchedule);
      Object.values(result).forEach(day => expect(day).toEqual([]));
    });

    it("should handle mixed case day names", () => {
      const mixedCaseSchedule: EquipmentSchedule[] = [
        {
          equipment: mockEquipment1,
          slots: [
            createMockSlot(1, "09:00", "10:00", "MON"),
            createMockSlot(2, "11:00", "12:00", "monday"),
          ],
          block_schedules: [
            createMockBlockSchedule(1, "14:00", "15:00", "MONDAY"),
            createMockBlockSchedule(2, "15:00", "16:00", "Mon"),
          ],
        },
      ];
      const result = groupEquipmentSchedulesByDay(mixedCaseSchedule);
      expect(result["mon"]).toHaveLength(1);
      const mondayEquipment = result["mon"]?.[0];
      expect(mondayEquipment?.slots).toHaveLength(2);
      expect(mondayEquipment?.block_schedules).toHaveLength(2);
    });

    it("should handle undefined day_of_week in slots and block_schedules", () => {
      const undefinedDaySchedule: EquipmentSchedule[] = [
        {
          equipment: mockEquipment1,
          slots: [
            {
              ...createMockSlot(1, "09:00", "10:00", "Mon"),
              day_of_week: undefined,
            },
          ],
          block_schedules: [
            {
              ...createMockBlockSchedule(1, "14:00", "15:00", "Mon"),
              day_of_week: undefined,
            },
          ],
        },
      ];
      const result = groupEquipmentSchedulesByDay(undefinedDaySchedule);
      Object.values(result).forEach(day => expect(day).toEqual([]));
    });

    it("should handle invalid day names", () => {
      const invalidDaySchedule: EquipmentSchedule[] = [
        {
          equipment: mockEquipment1,
          slots: [
            {
              ...createMockSlot(1, "09:00", "10:00", "Mon"),
              day_of_week: "invalid",
            },
          ],
          block_schedules: [
            {
              ...createMockBlockSchedule(1, "14:00", "15:00", "Mon"),
              day_of_week: "notaday",
            },
          ],
        },
      ];
      const result = groupEquipmentSchedulesByDay(invalidDaySchedule);
      Object.values(result).forEach(day => expect(day).toEqual([]));
    });

    it("should preserve equipment details in grouped results", () => {
      const result = groupEquipmentSchedulesByDay(mockSchedule);
      const mondayEquipment = result["mon"]?.find(
        e => e.equipment.id === 1
      )?.equipment;

      expect(mondayEquipment).toEqual(mockEquipment1);
    });

    it("should handle equipment with only block schedules", () => {
      const blockOnlySchedule: EquipmentSchedule[] = [
        {
          equipment: mockEquipment1,
          slots: [],
          block_schedules: [
            createMockBlockSchedule(
              1,
              "09:00",
              "10:00",
              "Mon",
              "Morning Block"
            ),
            createMockBlockSchedule(
              2,
              "14:00",
              "15:00",
              "Mon",
              "Afternoon Block"
            ),
          ],
        },
      ];
      const result = groupEquipmentSchedulesByDay(blockOnlySchedule);
      expect(result["mon"]).toHaveLength(1);
      const mondayEquipment = result["mon"]?.[0];
      expect(mondayEquipment?.slots).toHaveLength(0);
      expect(mondayEquipment?.block_schedules).toHaveLength(2);
      expect(mondayEquipment?.block_schedules?.[0]?.category).toBe(
        "Morning Block"
      );
      expect(mondayEquipment?.block_schedules?.[1]?.category).toBe(
        "Afternoon Block"
      );
    });

    it("should group equipment by day with user reservations", () => {
      const result = groupEquipmentSchedulesByDay(mockSchedule);

      // Check Monday (has slots, block schedules, and user reservations)
      expect(result["mon"]).toHaveLength(2);
      const mondayEquip1 = result["mon"]?.find(e => e.equipment.id === 1);
      const mondayEquip2 = result["mon"]?.find(e => e.equipment.id === 2);

      expect(mondayEquip1?.current_user_reservations).toHaveLength(1);
      expect(mondayEquip1?.current_user_reservations?.[0]?.reservation_id).toBe(
        1
      );
      expect(
        mondayEquip1?.current_user_reservations?.[0]?.start_date_time
      ).toBe("2024-03-18 13:00:00");

      expect(mondayEquip2?.current_user_reservations).toHaveLength(1);
      expect(mondayEquip2?.current_user_reservations?.[0]?.reservation_id).toBe(
        3
      );

      // Check Tuesday (has slots and user reservations)
      expect(result["tue"]).toHaveLength(2);
      const tuesdayEquip1 = result["tue"]?.find(e => e.equipment.id === 1);
      expect(tuesdayEquip1?.current_user_reservations).toHaveLength(1);
      expect(
        tuesdayEquip1?.current_user_reservations?.[0]?.reservation_id
      ).toBe(2);
    });

    it("should handle equipment with only user reservations", () => {
      const reservationsOnlySchedule: EquipmentSchedule[] = [
        {
          equipment: mockEquipment1,
          slots: [],
          block_schedules: [],
          current_user_reservations: [
            createMockReservedSchedule(
              1,
              "2024-03-18 09:00:00",
              "2024-03-18 10:00:00"
            ),
            createMockReservedSchedule(
              2,
              "2024-03-18 14:00:00",
              "2024-03-18 15:00:00"
            ),
          ],
        },
      ];
      const result = groupEquipmentSchedulesByDay(reservationsOnlySchedule);
      expect(result["mon"]).toHaveLength(1);
      const mondayEquipment = result["mon"]?.[0];
      expect(mondayEquipment?.slots).toHaveLength(0);
      expect(mondayEquipment?.block_schedules).toHaveLength(0);
      expect(mondayEquipment?.current_user_reservations).toHaveLength(2);
      expect(
        mondayEquipment?.current_user_reservations?.[0]?.reservation_id
      ).toBe(1);
      expect(
        mondayEquipment?.current_user_reservations?.[1]?.reservation_id
      ).toBe(2);
    });

    it("should handle invalid dates in user reservations", () => {
      const invalidDateSchedule: EquipmentSchedule[] = [
        {
          equipment: mockEquipment1,
          slots: [],
          block_schedules: [],
          current_user_reservations: [
            {
              id: 1,
              reservation_id: 1,
              start_date_time: "invalid-date",
              end_date_time: "invalid-date",
              start_time: "00:00",
              end_time: "00:00",
              day_of_week: "invalid",
            },
          ],
        },
      ];
      const result = groupEquipmentSchedulesByDay(invalidDateSchedule);
      Object.values(result).forEach(day => expect(day).toEqual([]));
    });
  });
});
