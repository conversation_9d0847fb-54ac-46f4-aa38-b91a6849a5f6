import { DAYS_OF_WEEK } from "@/common/common.utils";
import { format } from "date-fns";
import {
  BlockSchedule,
  EquipmentSchedule,
  EquipmentSlot,
  EquipmentType,
  ReservedSchedule,
} from "../types";

type GroupedSlots = {
  [key: string]: EquipmentSlot[];
};

type GroupedEquipment = {
  [key: string]: EquipmentSchedule[];
};

type DayOfWeek = (typeof DAYS_OF_WEEK)[number]["value"];

const isDayOfWeek = (day: string): day is DayOfWeek => {
  return DAYS_OF_WEEK.map(d => d.value).includes(day as DayOfWeek);
};

const normalizeDay = (day: string | undefined): DayOfWeek | undefined => {
  if (!day) {
    return undefined;
  }
  const normalizedDay = day.toLowerCase().substring(0, 3) as DayOfWeek;
  return isDayOfWeek(normalizedDay) ? normalizedDay : undefined;
};

const initializeEmptySchedule = (): GroupedEquipment => {
  return DAYS_OF_WEEK.reduce(
    (acc, day) => ({ ...acc, [day.value]: [] }),
    {} as GroupedEquipment
  );
};

const findOrCreateEquipmentEntry = (
  dayEquipments: EquipmentSchedule[],
  equipment: EquipmentType
): EquipmentSchedule => {
  const existingEntry = dayEquipments.find(
    e => e.equipment.id === equipment.id
  );
  if (existingEntry) {
    return existingEntry;
  }

  return {
    equipment,
    slots: [],
    block_schedules: [],
  };
};

const addSlotToEquipment = (
  result: GroupedEquipment,
  day: DayOfWeek,
  equipment: EquipmentType,
  slot: EquipmentSlot
): void => {
  if (!result[day]) {
    result[day] = [];
  }

  const equipments = result[day]!;
  const dayEquipment = findOrCreateEquipmentEntry(equipments, equipment);

  if (!equipments.some(e => e.equipment.id === equipment.id)) {
    equipments.push(dayEquipment);
  }

  dayEquipment.slots.push(slot);
};

const addBlockScheduleToEquipment = (
  result: GroupedEquipment,
  day: DayOfWeek,
  equipment: EquipmentType,
  blockSchedule: BlockSchedule
): void => {
  if (!result[day]) {
    result[day] = [];
  }

  const equipments = result[day]!;
  const dayEquipment = findOrCreateEquipmentEntry(equipments, equipment);

  if (!equipments.some(e => e.equipment.id === equipment.id)) {
    equipments.push(dayEquipment);
  }

  if (!dayEquipment.block_schedules) {
    dayEquipment.block_schedules = [];
  }
  dayEquipment.block_schedules.push(blockSchedule);
};

const addReservationToEquipment = (
  result: GroupedEquipment,
  day: DayOfWeek,
  equipment: EquipmentType,
  reservation: ReservedSchedule
): void => {
  if (!result[day]) {
    result[day] = [];
  }

  const equipments = result[day]!;
  const dayEquipment = findOrCreateEquipmentEntry(equipments, equipment);

  if (!equipments.some(e => e.equipment.id === equipment.id)) {
    equipments.push(dayEquipment);
  }

  if (!dayEquipment.current_user_reservations) {
    dayEquipment.current_user_reservations = [];
  }
  dayEquipment.current_user_reservations.push(reservation);
};

const processSlots = (
  result: GroupedEquipment,
  equipmentSchedule: EquipmentSchedule
): void => {
  const { equipment, slots = [] } = equipmentSchedule;

  slots.forEach(slot => {
    const day = normalizeDay(slot.day_of_week);
    if (!day) {
      return;
    }
    addSlotToEquipment(result, day, equipment, slot);
  });
};

const processBlockSchedules = (
  result: GroupedEquipment,
  equipmentSchedule: EquipmentSchedule
): void => {
  const { equipment, block_schedules = [] } = equipmentSchedule;

  block_schedules.forEach(blockSchedule => {
    const day = normalizeDay(blockSchedule.day_of_week);
    if (!day) {
      return;
    }
    addBlockScheduleToEquipment(result, day, equipment, blockSchedule);
  });
};

const processCurrentUserReservations = (
  result: GroupedEquipment,
  equipmentSchedule: EquipmentSchedule
): void => {
  const { equipment, current_user_reservations = [] } = equipmentSchedule;

  current_user_reservations.forEach(reservation => {
    try {
      const startDate = new Date(reservation.start_date_time);
      const day = normalizeDay(format(startDate, "EEEE").toLowerCase());

      if (!day) {
        return;
      }

      addReservationToEquipment(result, day, equipment, reservation);
    } catch {
      return;
    }
  });
};

export const groupEquipmentSlotsByStartTime = (
  slots: EquipmentSlot[]
): GroupedSlots => {
  return slots.reduce((acc: GroupedSlots, slot) => {
    const startTime = slot.start_time;
    if (typeof startTime !== "string") {
      return acc;
    }

    if (!acc[startTime]) {
      acc[startTime] = [];
    }

    (acc[startTime] as EquipmentSlot[]).push(slot);
    return acc;
  }, {} as GroupedSlots);
};

export const groupEquipmentSchedulesByDay = (
  schedule: EquipmentSchedule[] | undefined
): GroupedEquipment => {
  const result = initializeEmptySchedule();

  if (!schedule) {
    return result;
  }

  schedule.forEach(equipmentSchedule => {
    processSlots(result, equipmentSchedule);
    processBlockSchedules(result, equipmentSchedule);
    processCurrentUserReservations(result, equipmentSchedule);
  });

  return result;
};
