/* STYLE OVERRIDES */

/* VALID STRIPES STYLES */
.md-success-stripes-bg {
  background: repeating-linear-gradient(
    -45deg,
    #fcfffc,
    #fcfffc 10px,
    #ecffdc 10px,
    #ecffdc 20px
  );
}

.md-danger-stripes-bg {
  background: repeating-linear-gradient(
    -45deg,
    #fcfffc,
    #fcfffc 10px,
    #ffeef4 10px,
    #ffeef4 20px
  );
}

.md-danger-stripes-bg .mbsc-schedule-color-text {
  color: #aa0000;
  font-weight: bolder;
}

.md-success-dots-bg {
  background-image: radial-gradient(#74dfa8 20%, transparent 20%);
  background-color: #eefbec;
  background-position:
    0 0,
    10px 10px;
  background-size: 20px 20px;
}

.mbsc-calendar-controls {
  padding: 0.1em !important;
}

.mbsc-calendar-header {
  /* overflow-x: auto; */
}

/* Date selector input in nav header */
.nav-calendar-selector input {
  min-width: 120px;
  text-align: center;
  background-color: #f7f7f7 !important;
}

/* Current Date Selection Text */
.mbsc-timeline-header-date .mbsc-timeline-header-date-text {
  font-weight: bolder;
}

@media (max-width: 767px) {
  .mbsc-timeline-resource-col,
  .mbsc-timeline-sidebar-col {
    width: 7em;
  }
}

span.mbsc-icon {
  width: 35px !important;
  height: 35px !important;
}
