// src/app/equipment/[orgId]/calendar/event-details.tsx
import { ClassDetailsResponse } from "@/app/classes/[orgId]/types";
import { obtainDateFrame } from "@/app/classes/[orgId]/utils";
import { CancelButton } from "@/components/custom/cancel-button";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { format } from "date-fns";
import { Fragment, ReactNode } from "react";

const obtainDetails = (
  data?: ClassDetailsResponse & { color?: string; equ_name?: string }
): ClassDetailsResponse & { isBlockSchedule?: boolean } => {
  {
    /* TODO: Temporal check for block events */
  }
  switch (data?.color) {
    case "red": {
      return {
        ...data,
        title: data.equ_name,
        name: data?.title as string,
        isBlockSchedule: true,
      };
    }
    default: {
      return {
        ...data,
        title: data?.equipment_name,
        name: `${data?.first_name} ${data?.last_name}`,
      } as ClassDetailsResponse;
    }
  }
};

const formatTime = (time: string) => {
  return format(time, "h:mmaaa");
};

const formatDate = (date?: string) => {
  if (!date) {
    return "-";
  }
  return format(date, "MMM d, uuuu");
};

const DAYS = [
  { value: "MO", label: "Monday" },
  { value: "TU", label: "Tuesday" },
  { value: "WE", label: "Wednesday" },
  { value: "TH", label: "Thursday" },
  { value: "FR", label: "Friday" },
  { value: "SA", label: "Saturday" },
  { value: "SU", label: "Sunday" },
];

const BlockRecurring = ({
  startTime,
  endTime,
  recurring,
}: {
  startTime?: string;
  endTime?: string;
  recurring?: Record<string, string | number>;
}) => {
  if (!recurring) {
    return null;
  }
  const weekDays = recurring?.weekDays?.toString().split(",");

  const days = weekDays
    ?.map(day => DAYS.find(d => d.value === day)?.label)
    ?.join(", ");

  return (
    <Fragment>
      <p className='mb-2'>
        Time:
        <span className='font-semibold'>
          {` ${formatTime(startTime ?? "")} - ${formatTime(endTime ?? "")}`}
        </span>
      </p>
      <p>
        <span>Recurring weekly every</span>
        <span className='font-bold pr-2 pl-2'>{days}</span>
        until
        <span className='font-bold pr-2 pl-2'>
          {formatDate(recurring?.until?.toString()?.split(" ")?.[0])}
        </span>
      </p>
    </Fragment>
  );
};

// New EventHeader component
const EventHeader = ({
  title,
  isBlockSchedule,
}: {
  isBlockSchedule?: boolean;
  title?: string;
}) => (
  <Fragment>
    <p className='font-extrabold mt-4'>{title}</p>
    {isBlockSchedule ? (
      <h2 className='font-extrabold'>Block Schedule</h2>
    ) : (
      <h2 className='font-extrabold'>Details</h2>
    )}
  </Fragment>
);

// New EventInfo component
const EventInfo = ({
  name,
  startTime,
  start,
  end,
  gymName,
  attendingPerson,
  recurring,
  isBlockSchedule,
}: {
  name?: string;
  startTime?: string;
  start?: string;
  end?: string;
  gymName?: string;
  attendingPerson?: number;
  recurring?: ReactNode;
  isBlockSchedule?: boolean;
}) => (
  <div className='flex gap-2'>
    <div
      className={`h-full w-1 ${isBlockSchedule ? "bg-red-400" : "bg-green-500"}`}
    ></div>
    <div>
      <p className='mb-2'>
        Name: <span className='font-semibold'>{name}</span>
      </p>
      <p className='mb-2'>
        Date: <span className='font-semibold'>{formatDate(startTime)}</span>
      </p>

      {recurring ? (
        <Fragment>{recurring}</Fragment>
      ) : (
        <p className='mb-2'>
          Time:{" "}
          <span className='font-semibold'>
            {obtainDateFrame(start as string, end as string)}
          </span>
        </p>
      )}

      {gymName && (
        <p className='mb-2'>
          Location: <span className='font-semibold'>{gymName}</span>
        </p>
      )}
      {attendingPerson && (
        <p className='mb-2'>
          Attending Persons:{" "}
          <span className='font-semibold'>{attendingPerson}</span>
        </p>
      )}
    </div>
  </div>
);

// New EventActions component
const EventActions = ({
  onClose,
  data,
}: {
  onClose: () => void;
  data?: ClassDetailsResponse;
}) => (
  <div className='flex items-center gap-2'>
    <Button variant={"outline"} onClick={onClose}>
      Cancel
    </Button>
    <CancelButton id={data?.id} onSuccess={onClose} />
  </div>
);

export const EventDetails = ({
  isOpen,
  onClose,
  data,
}: {
  isOpen: boolean;
  onClose: () => void;
  data?: ClassDetailsResponse & {
    color?: string;
    equ_name?: string;
    recurring?: Record<string, string | number>;
  };
}) => {
  const { title, name, isBlockSchedule } = obtainDetails(data);
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='lg:w-[420px]'>
        <EventHeader title={title} isBlockSchedule={isBlockSchedule} />
        <EventInfo
          isBlockSchedule={isBlockSchedule}
          name={name}
          start={data?.start}
          end={data?.end}
          gymName={data?.gym_name}
          attendingPerson={data?.attending_persons}
          startTime={data?.start}
          recurring={
            <BlockRecurring
              startTime={data?.start}
              endTime={data?.end}
              recurring={data?.recurring}
            />
          }
        />

        {!isBlockSchedule && <EventActions onClose={onClose} data={data} />}
      </DialogContent>
    </Dialog>
  );
};
