import { EmbedPageViewTracker } from "@/app/classes/[orgId]/modules/analytics/EmbedPageViewTracker";
import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { LoginModal } from "@/components/custom/login-auth/login-modal";
import { LoginLogoutButton } from "@/components/custom/login-logout-button";
import { EventCalendarView } from "./calendar";

export default async function Schedule({
  params,
  searchParams,
}: Readonly<{
  params: {
    orgId: string;
  };
  searchParams: Record<string, string | number>;
}>) {
  const { facilitiesOptions, equipmentCategoriesOptions, configs, appUrls } =
    await fetchClientConfigs(params.orgId);

  return (
    <div className='overflow-auto h-screen'>
      <EmbedPageViewTracker pageTitle='Equipment Calendar Page' />
      <div className='flex justify-end container lg:mb-2'>
        <LoginLogoutButton />
      </div>
      <EventCalendarView
        facilitiesOptions={facilitiesOptions}
        categoriesOptions={equipmentCategoriesOptions}
        searchParams={searchParams}
        orgId={params.orgId}
      />
      <LoginModal
        buttonColor={configs?.embed?.button_color}
        appUrls={appUrls}
        configs={configs}
        orgId={params.orgId}
      />
    </div>
  );
}
