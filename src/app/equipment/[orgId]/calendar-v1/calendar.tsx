// src/app/equipment/[orgId]/calendar/calendar.tsx
/* eslint-disable import/no-unassigned-import */
"use client";

import {
  Eventcalendar,
  MbscCellClickEvent,
  MbscEventClickEvent,
} from "@mobiscroll/react";
import {
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import "@mobiscroll/react/dist/css/mobiscroll.min.css";

import "./calendar.css";

import { useStoreValue } from "@/app/StoreContext";
import { ReserveSuccessModal } from "@/app/classes/[orgId]/modules/overview-block/reserve-success-modal";
import { useUserReservations } from "@/app/classes/[orgId]/queries/useUserReservations";
import { ClassDetailsResponse } from "@/app/classes/[orgId]/types";
import { formatDate } from "@/common/common.utils";
import { useApplyStateToUrl } from "@/common/hooks/useApplyStateToUrl";
import { Option } from "@/components/ui/select";
import { format } from "date-fns";
import { flatMap, isEmpty } from "lodash/fp";
import { Loader } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useEquipmentWeeklyData } from "../queries/useWeeklyEquipmentData";
import { EquipmentType } from "../types";
import { EventDetails } from "./event-details";
import { Filters } from "./filters";
import { CalendarHeaders } from "./headers";
import { Reservation } from "./reservation";
import { useBlockSchedules } from "./useBlockSchedules";
import { craftReservationEvent, obtainAvailabilityData } from "./utils";

export const EventCalendarView = ({
  searchParams,
  orgId,
  facilitiesOptions,
  categoriesOptions,
}: {
  categoriesOptions: Option[];
  facilitiesOptions: Option[];
  searchParams: Record<string, string | number>;
  orgId?: string;
}) => {
  const [selected, setSelected] = useState<EquipmentType | undefined>();

  const [selectedEvent, setSelectedEvent] =
    useState<ClassDetailsResponse | null>();

  const date = useSearchParams().get("date") ?? new Date();

  const { data, isPending } = useEquipmentWeeklyData({
    orgId: orgId as string,
    searchParams: {
      facility_id: searchParams?.facility_id ?? "",
      equipment_type_id: searchParams?.equipment_type_id ?? "",
      start_date: formatDate(date),
      end_date: formatDate(date),
    },
    enabledRequest: true,
    xSource: "EMBED",
  });

  const resources = useMemo(
    () =>
      data?.map(resource => ({
        id: resource.equipment.id,
        name: resource.equipment.name,
      })),
    [data]
  );

  const availability = useMemo(
    () =>
      flatMap(rec => {
        return obtainAvailabilityData(rec.slots, rec.equipment.id);
      }, data),
    [data]
  );

  const applyToUrl = useApplyStateToUrl();

  const onSelectedDateChange = (event: MbscEventClickEvent) =>
    applyToUrl({ date: format(event.date, "yyyy-MM-dd") });

  const { state: reservations, dispatch } = useStoreValue(
    state => state.reservation
  );

  const { data: reservationData } = useUserReservations(true);

  const updatedReservation = useMemo(() => {
    const found = reservationData?.find(
      item => item.equipment_id === reservations?.id
    );

    if (found) {
      return {
        ...found,
        ...found?.current_user_reservation,
        name: `${found.equipment_name}`,
        start_time: found?.start_time?.toString().split(" ")[1],
        end_time: found?.end_time?.toString().split(" ")[1],
      };
    }
    return null;
  }, [reservationData, reservations?.id]);

  const nav = useCallback(
    () => (
      <div className='w-full bg-white lg:pt-4 bottom-0'>
        <Filters
          facilitiesOptions={facilitiesOptions}
          categoriesOptions={categoriesOptions}
          searchParams={searchParams}
        />
        <CalendarHeaders />
      </div>
    ),
    [categoriesOptions, facilitiesOptions, searchParams]
  );

  const handleCellClick = useCallback(
    (e: MbscCellClickEvent) => {
      const foundData = data?.find(
        rec => rec.equipment.id === e.resource
      )?.equipment;

      if (foundData) {
        return setSelected({
          ...foundData,
          date: e.date,
          id: e.resource as number,
        });
      }
    },
    [data]
  );

  useEffect(() => {
    if (updatedReservation) {
      setSelected(undefined);
    }
  }, [updatedReservation]);

  const defaultValue = useRef({ facility_id: searchParams?.facility_id });
  const applyToUrlRef = useRef(applyToUrl);
  const facilitiesRef = useRef(facilitiesOptions);

  useEffect(() => {
    if (!defaultValue.current?.facility_id) {
      applyToUrlRef.current({
        facility_id: String(facilitiesRef.current?.[1]?.value) || "",
      });
    }
  }, []);

  const { data: blockSchedules } = useBlockSchedules(orgId as string, {
    start_date: formatDate(date),
    end_date: formatDate(date),
  });

  const reservedEvents = useMemo(
    () => craftReservationEvent(reservationData),
    [reservationData]
  );

  const onEventClick = (e: MbscEventClickEvent) =>
    setSelectedEvent(e.event as ClassDetailsResponse);

  if (isPending) {
    return (
      <div className='flex justify-center items-center'>
        <Loader className='w-14 h-14 animate-spin' />
      </div>
    );
  }

  return (
    <Fragment>
      <Eventcalendar
        className='mt-16 lg:mt-0 md:mt-0 lg:container'
        selectedDate={date}
        renderHeader={nav}
        theme='ios'
        themeVariant='light'
        view={{
          timeline: {
            type: "day",
            timeCellStep: 30,
            timeLabelStep: 30,
            startTime: "05:00",
          },
        }}
        onSelectedDateChange={onSelectedDateChange}
        resources={resources}
        colors={[
          [...(data ?? [])].map(val => ({
            ...val,
            allDay: true,
            color: "red",
            title: "hello",
          })),
          ...(availability || []),
          ...(blockSchedules || []).map(blockSchedule => {
            return {
              ...blockSchedule,
              title: "",
            };
          }),
        ]}
        data={[
          ...(blockSchedules || []).map(blockSchedule => {
            return {
              ...blockSchedule,
              color: "red",
            };
          }),
          ...(reservedEvents || []),
        ]}
        touchUi
        onCellClick={handleCellClick}
        exclusiveEndDates
        onEventClick={onEventClick}
      />
      {Boolean(selected) && (
        <Reservation
          isOpen={Boolean(selected)}
          onClose={() => setSelected(undefined)}
          data={selected}
        />
      )}
      {!isEmpty(updatedReservation) && (
        <ReserveSuccessModal
          reservations={updatedReservation}
          onClose={() => dispatch(() => ({ reservation: null }))}
          paramDate={searchParams?.date as string}
          embedType='equipment'
        />
      )}

      {!isEmpty(selectedEvent) && (
        <EventDetails
          isOpen={Boolean(selectedEvent)}
          onClose={() => setSelectedEvent(undefined)}
          data={selectedEvent}
        />
      )}
    </Fragment>
  );
};
