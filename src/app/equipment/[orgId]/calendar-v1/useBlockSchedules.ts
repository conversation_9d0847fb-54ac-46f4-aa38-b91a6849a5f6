"use client";

import { BASE_API_URL_CLIENT } from "@/app/classes/[orgId]/actions/constant";
import { useQuery } from "@tanstack/react-query";

export interface BlockSchedule {
  data: {
    id: number;
    cssClass: string;
    resource: number;
    start: string;
    end: string;
    title: string;
    trainer_name: string;
    equ_name: string;
  }[];
  success?: string;
}

export const fetchBlockSchedules = async (
  orgId: string,
  params: Record<string, string | number>
): Promise<BlockSchedule> => {
  try {
    const param = {
      ...params,
      start_date: `${params.start_date} 00:00:00`,
      end_date: `${params.end_date} 23:59:59`,
    };
    const urlParams = new URLSearchParams({
      ...param,
      university_id: orgId,
    }).toString();
    const response = await fetch(
      `${BASE_API_URL_CLIENT}/blockschedules/unauth?${urlParams}`
    );
    return await response.json();
  } catch (err) {
    throw new Error("Sorry, there was an error fetching the data.");
  }
};

export const useBlockSchedules = (
  orgId: string,
  searchParams: Record<string, string | number>
) =>
  useQuery({
    queryKey: [orgId, searchParams],
    staleTime: 30000,
    refetchInterval: 30 * 1000,
    refetchIntervalInBackground: true,
    select: rec => rec.data,
    queryFn: () => fetchBlockSchedules(orgId, searchParams),
  });
