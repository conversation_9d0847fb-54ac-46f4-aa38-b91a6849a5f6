import { Option } from "@/components/ui/select";

export interface EquipmentProps {
  end_time?: string;
  start_time?: string;
  id: number;
  gym_id: number;
  room_id: number;
  total_slots: number;
  name: string;
  use_room_times: number;
  duration_time_interval: number;
  max_reservations: number;
  min_time_lt: string;
  mac_time_lt: string;
  gym_name: string;
  room_name: string;
  day_of_week: string;
  uniq: string;
  facility_closed: boolean;
  time_slots?: TimeSlotsEntity[] | null;
  slots_available: number;
  available_time_slots?: AvailableTimeSlotsEntity[] | null;
  durations?: Option[] | null;
  attending_persons?: Option[] | null;
  next_available_time_slot?: AvailableTimeSlotsEntity | null;
  status?: string;
  current_user_reservation?: Record<string, string | number>;
  current_status?: string;
}
export interface TimeSlotsEntity {
  equ_slot_id: number;
  start_time: string;
  end_time: string;
  day_of_week: string;
}
export interface AvailableTimeSlotsEntity {
  time_value: string;
  time_label: string;
  reservations_at_slot: number;
  remarks: string;
  allow_reservations: boolean;
}
export interface DurationsEntity {
  value: number;
  label: string;
}

export interface EquipmentWeeklyType {
  schedule: EquipmentSchedule[];
}

export type BlockSchedule = {
  block_schedule_id: string;
  equipment_id: string;
  category: string;
  message: string;
} & EquipmentSlot;

export type ReservedSchedule = {
  reservation_id: number;
  start_date_time: string;
  end_date_time: string;
} & EquipmentSlot;

export interface EquipmentSchedule {
  equipment: EquipmentType;
  slots: EquipmentSlot[];
  block_schedules?: BlockSchedule[];
  current_user_reservations?: ReservedSchedule[];
}

export interface EquipmentType {
  id: number;
  name: string;
  facility: string;
  description: string;
  type: string;
  image_url: string;
  attending_persons?: Option[];
  durations?: Option[];
  date?: Date;
  equipment_name?: string;
}

export interface EquipmentSlot {
  id: number;
  start_time: string;
  end_time: string;
  day_of_week?: string;
}

export type SelectedEquipmentSchedule = EquipmentSchedule & {
  selectedDate: string;
};

export type EquipmentAvailableTime = {
  time_value: string;
  time_label: string;
  full_date_time: string;
  minimum_end_time: string;
  full_minimum_end_time: string;
  reservations_at_slot: number;
  remarks: string;
  allow_reservations: boolean;
};
