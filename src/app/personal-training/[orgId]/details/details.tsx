"use client";

import { isMobileOnly } from "react-device-detect";
import { DesktopModal } from "./desktop-modal";
import { MobileModal } from "./mobile-modal";
import { Dispatch, SetStateAction } from "react";
import { TrainerType } from "../types";

export interface DetailModalProps {
  isOpen: boolean;
  setIsOpen?: Dispatch<SetStateAction<TrainerType | null>>;
  data?: TrainerType;
}

export const DetailModal = (props: DetailModalProps) => {
  if (isMobileOnly) {
    return <MobileModal {...props} />;
  }

  return <DesktopModal {...props} />;
};
