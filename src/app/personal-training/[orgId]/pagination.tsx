"use client";
import { useStoreValue } from "@/app/StoreContext";
import { useApplyStateToUrl } from "@/common/hooks/useApplyStateToUrl";
import {
  PaginationContent,
  PaginationItem,
  PaginationLink,
} from "@/components/ui/pagination";
import { isEmpty } from "lodash/fp";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { styled } from "styled-components";
import { useTrainers } from "./queries/useTrainers";
import { LinkType } from "./types";

const StyledPaginationItem = styled(PaginationLink)<{ color?: string }>`
  color: ${({ color }) => color};
`;

interface PaginationData {
  data: any[];
  links: LinkType[];
  prev_page_url: string | null;
  next_page_url: string | null;
  total: number;
}

const extractPageNumber = (url: string | null): string | null => {
  if (!url) {
    return null;
  }
  return url.match(/\?page=(\d+)/)?.[1] ?? null;
};

const scrollToTop = () => window.scrollTo({ top: 0, behavior: "smooth" });

export const Pagination = ({
  orgId,
  searchParams,
}: {
  orgId: string;
  searchParams: Record<string, string | number>;
}) => {
  const { data } = useTrainers(orgId, searchParams) as { data: PaginationData };
  const applyToUrl = useApplyStateToUrl();
  const { state: configs } = useStoreValue(state => state.configs);

  const paginationLinks = data?.links ? data.links.slice(1, -1) : [];
  const previousPageNumber = extractPageNumber(data?.prev_page_url);
  const nextPageNumber = extractPageNumber(data?.next_page_url);

  if (isEmpty(data?.data) || (data?.total ?? 0) < 20) {
    return null;
  }

  const handlePageChange = (pageNumber: string) => {
    scrollToTop();
    applyToUrl({ page: pageNumber });
  };

  return (
    <PaginationContent className='flex items-center justify-center p-4 mt-4 mb-4 sticky bottom-0 bg-white sm:pl-1s'>
      <ChevronLeft
        onClick={() =>
          previousPageNumber && handlePageChange(previousPageNumber)
        }
        color={previousPageNumber ? configs?.button_color : "#ccc"}
        cursor={previousPageNumber ? "pointer" : "not-allowed"}
        size={30}
        aria-label='Previous page'
        role='button'
        aria-disabled={!previousPageNumber}
      />

      {paginationLinks?.map((link: LinkType) => (
        <PaginationItem key={link.label}>
          <StyledPaginationItem
            onClick={e => {
              e.preventDefault();
              if (link?.url) {
                handlePageChange(link.label);
              }
            }}
            isActive={link.active}
            href={String(link?.url)}
            color={link.active ? configs?.button_color : ""}
            aria-current={link.active ? "page" : undefined}
            aria-label={`Page ${link.label}`}
          >
            {link?.label}
          </StyledPaginationItem>
        </PaginationItem>
      ))}

      <ChevronRight
        onClick={() => nextPageNumber && handlePageChange(nextPageNumber)}
        color={nextPageNumber ? configs?.button_color : "#ccc"}
        cursor={nextPageNumber ? "pointer" : "not-allowed"}
        size={30}
        aria-label='Next page'
        role='button'
        aria-disabled={!nextPageNumber}
      />
    </PaginationContent>
  );
};
