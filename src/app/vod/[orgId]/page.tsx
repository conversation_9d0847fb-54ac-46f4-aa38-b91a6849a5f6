/* eslint-disable import/no-unassigned-import */

import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { LoginModal } from "@/components/custom/login-auth/login-modal";
import CategoriesPage from "./components/CategoriesPage";

export default async function VODPage({
  params,
}: Readonly<{
  params: {
    orgId: string;
  };
}>) {
  const { orgId } = params;
  const { configs, appUrls } = await fetchClientConfigs(params.orgId);

  return (
    <>
      <div className='p-4'>
        <div className='flex justify-center'>
          <CategoriesPage orgId={orgId} />
        </div>
      </div>

      <LoginModal
        buttonColor={configs?.embed?.button_color}
        appUrls={appUrls}
        configs={configs}
        orgId={params.orgId}
      />
    </>
  );
}
