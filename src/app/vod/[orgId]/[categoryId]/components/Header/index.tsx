import { LoginLogoutButton } from "@/components/custom/login-logout-button";
import { Categories } from "./Categories";

export const Header = ({
  orgId,
  handleSearch,
  selectedCategoryId,
  setSelectedCategoryId,
}: {
  orgId: string;
  selectedCategoryId: string | null;
  handleSearch: (searchTerm: string) => void;
  setSelectedCategoryId: (categoryId: string) => void;
}) => {
  // const router = useRouter();
  return (
    <div className='grid grid-cols-1 gap-[30px] mt-3'>
      <div className='flex flex-col-reverse lg:flex-row justify-between mb-1 items-center gap-3 gap-5 md:gap-0'>
        {/* <button
          type='button'
          className='text-sm text-gray-500'
          onClick={() => router.back()}
        >
          <FaArrowLeft className='text-2xl' />
        </button> */}
        <div className='flex flex-col-reverse lg:flex-row gap-3 justify-end items-center w-full'>
          <div className='flex-1 w-full'>
            <input
              type='text'
              placeholder='Search videos by name, category, instructor, level, equipment'
              className='w-full md:w-full h-[40px] border border-[#E5E7ED] px-[10px] py-[5px] text-[15px] bg-white placeholder:text-[#999999] active:border-[#bcbdbd] focus:border-[#bcbdbd] outline-none rounded-[15px]'
              onChange={e => handleSearch(e.target.value)}
            />
          </div>
          {/* <SelectDropdown
            value=''
            options={[]}
            onChange={() => {}}
            placeholder='Filter by instructor'
            className='w-full lg:w-auto min-w-[150px] rounded-[15px] border-[#E5E7ED] h-[40px]'
          />
          <SelectDropdown
            value=''
            options={[]}
            onChange={() => {}}
            placeholder='Filter by level'
            className='w-full lg:w-auto min-w-[150px] rounded-[15px] border-[#E5E7ED] h-[40px]'
          />

          <SelectDropdown
            value=''
            options={[]}
            onChange={() => {}}
            placeholder='Filter by equipment'
            className='w-full lg:w-auto min-w-[150px] rounded-[15px] border-[#E5E7ED] h-[40px]'
          /> */}

          <div>
            <LoginLogoutButton
              ctaLabel='Login to watch videos'
              unauthenticatedCtaClassName='mt-0'
              authenticatedCtaClassName='mt-0'
            />
          </div>
        </div>
      </div>
      <Categories
        orgId={orgId}
        selectedCategoryId={selectedCategoryId}
        setSelectedCategoryId={setSelectedCategoryId}
      />
    </div>
  );
};
