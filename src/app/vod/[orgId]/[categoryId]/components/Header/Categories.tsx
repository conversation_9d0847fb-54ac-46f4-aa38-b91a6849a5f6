"use client";

import {
  useFetchVoDCategories,
  VoDCategory,
} from "@/app/vod/queries/useFetchVoDCategories";
import { useSession } from "@/components/custom/login-auth/auth-provider";

const SkeletonLoader = () => {
  return (
    <div className='flex items-center gap-3 mb-3'>
      {Array.from({ length: 5 }).map((_, index) => (
        <div
          key={index}
          className='cursor-pointer rounded-[15px] font-semibold text-sm border px-4 py-2 bg-gray-300 animate-pulse w-[120px] h-[40px]'
        ></div>
      ))}
    </div>
  );
};

export const Categories = ({
  orgId,
  selectedCategoryId,
  setSelectedCategoryId,
}: {
  orgId: string;
  selectedCategoryId: string | null;
  setSelectedCategoryId: (categoryId: string) => void;
}) => {
  const { data: sessionData } = useSession();

  const { data, isPending } = useFetchVoDCategories({
    orgId: orgId,
    token: sessionData?.token,
  });

  if (isPending) {
    return <SkeletonLoader />;
  }

  return (
    <div className='flex flex-wrap items-center gap-3 mb-3'>
      {data?.map((category: VoDCategory) => (
        <span
          key={category.id}
          onClick={() => setSelectedCategoryId(category.id.toString())}
          className={`cursor-pointer rounded-[15px] font-semibold text-sm border px-4 py-2 ${selectedCategoryId === category.id.toString() ? "bg-[#009DC4] text-white" : "border-[#E5E7ED] bg-white text-black"}`}
        >
          {category.name}
        </span>
      ))}
    </div>
  );
};
