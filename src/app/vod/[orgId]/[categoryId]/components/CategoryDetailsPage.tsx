"use client";

import { useFetchVideoOnDemands } from "@/app/vod/queries/useFetchVideoOnDemands";
import { debounce } from "lodash/fp";
import { useParams } from "next/navigation";
import { useMemo, useState } from "react";
import { Header } from "./Header";
import { VideosList } from "./VideosList";

export default function CategoryDetailsPage() {
  const params = useParams();
  const [search, setSearch] = useState("");
  const [selectedCategoryId, setSelectedCategoryId] = useState(
    params.categoryId as string
  );

  const { data: videos = [], isPending: isVideosPending } =
    useFetchVideoOnDemands({
      orgId: params.orgId as string,
      categoryId: selectedCategoryId,
      search,
    });

  const debouncedSetSearch = useMemo(
    () =>
      debounce(300, (searchTerm: string) => {
        setSearch(searchTerm);
      }),
    []
  );

  const handleSearch = (searchTerm: string) => {
    debouncedSetSearch(searchTerm);
  };

  return (
    <div className='flex justify-center'>
      <div className='w-full max-w-[552px] lg:max-w-[1140px]'>
        <Header
          handleSearch={handleSearch}
          orgId={params.orgId as string}
          selectedCategoryId={selectedCategoryId}
          setSelectedCategoryId={setSelectedCategoryId}
        />
        <VideosList data={videos} isPending={isVideosPending} />
      </div>
    </div>
  );
}
