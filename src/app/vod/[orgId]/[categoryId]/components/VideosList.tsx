import { VideoOnDemand } from "@/app/vod/queries/useFetchVideoOnDemands";
import { Magnifier } from "@/components/icons/magnifier";
import { useState } from "react";
import VideoCard from "./VideoCard";
import { VideoModal } from "./VideoModal";

const SkeletonLoader = () => {
  return (
    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[30px] justify-items-end'>
      {Array.from({ length: 6 }).map((_, index) => (
        <div
          key={index}
          className='relative w-full w-max-[260px] lg:w-full lg:w-max-[360px] cursor-pointer'
        >
          {/* Video thumbnail skeleton */}
          <div className='thumbnail w-full h-[260px] overflow-hidden rounded-[12px] relative bg-gray-300 animate-pulse'>
            {/* Duration badge skeleton */}
            <div className='absolute bottom-[10px] left-[10px] z-10 bg-gray-400 animate-pulse w-[50px] h-[25px] rounded-[55px]'></div>
          </div>

          {/* Video title skeleton */}
          <div className='mt-3 mb-0 h-6 bg-gray-300 animate-pulse rounded w-3/4'></div>

          {/* Instructor skeleton */}
          <div className='mt-2 h-4 bg-gray-300 animate-pulse rounded w-full'></div>

          {/* Level skeleton */}
          <div className='mt-1 h-4 bg-gray-300 animate-pulse rounded w-1/2'></div>

          {/* Equipment skeleton */}
          <div className='mt-1 h-4 bg-gray-300 animate-pulse rounded w-2/3'></div>
        </div>
      ))}
    </div>
  );
};

export const VideosList = ({
  data,
  isPending,
}: {
  data: VideoOnDemand[];
  isPending: boolean;
}) => {
  const [selectedVideo, setSelectedVideo] = useState<VideoOnDemand | null>(
    null
  );

  if (isPending) {
    return <SkeletonLoader />;
  }

  if (data.length === 0) {
    return (
      <div className='flex flex-col items-center justify-center text-center mt-5 text-sm min-h-[500px]'>
        <Magnifier />
        <span className='block mt-2 mb-1 text-lg font-semibold'>
          No videos in this category yet.
        </span>
        <span className='block text-sm text-gray-400 '>
          Try exploring other categories
        </span>
      </div>
    );
  }

  return (
    <>
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[30px] justify-items-end'>
        {data.map(video => (
          <div key={video.id} className='w-full'>
            <VideoCard video={video} onClick={() => setSelectedVideo(video)} />
          </div>
        ))}
      </div>

      {selectedVideo && (
        <VideoModal
          video={selectedVideo}
          closeModal={() => setSelectedVideo(null)}
        />
      )}
    </>
  );
};

export default VideosList;
