import { VideoOnDemand } from "@/app/vod/queries/useFetchVideoOnDemands";
import Image from "next/image";
import { FaPlay } from "react-icons/fa";

interface VideoCardProps {
  video: VideoOnDemand;
  onClick: () => void;
}

export default function VideoCard({ video, onClick }: VideoCardProps) {
  return (
    <div
      className='relative w-full w-max-[260px] lg:w-full lg:w-max-[360px] cursor-pointer'
      onClick={onClick}
    >
      <div className='thumbnail w-full h-[260px] overflow-hidden rounded-[12px] relative'>
        <div className='absolute inset-0 flex items-center justify-center z-20 cursor-pointer'>
          <FaPlay className='text-white text-5xl' />
        </div>
        <span className='absolute bottom-[10px] left-[10px] z-10 bg-[#009DC4] text-white px-[18px] py-[5px] rounded-[55px] text-[12px] font-bold bg-opacity-80'>
          {video.duration_minutes}
        </span>
        {video.video_thumbnail ? (
          <Image
            src={video.video_thumbnail}
            alt={video.name}
            fill
            className='object-cover object-top rounded-t-[15px] transition-transform duration-300 ease-in-out hover:scale-110'
          />
        ) : (
          <div className='w-full h-[260px] relative overflow-hidden rounded-t-[12px] bg-gray-300 flex items-center justify-center'>
            <span className='bg-gray-300 rounded-full block w-full text-center text-xs text-gray-400'></span>
          </div>
        )}
      </div>
      <p
        className='text-lg font-semibold mt-3 mb-0 truncate'
        title={video.name}
      >
        {video.name}
      </p>
      <p className='text-sm text-gray-500'>
        <span className='font-semibold'>Instructor:</span>{" "}
        {video.instructor_first_name} {video.instructor_last_name}
      </p>
      <p className='text-sm text-gray-500'>
        <span className='font-semibold'>Level:</span> {video.level}
      </p>
      <p className='text-sm text-gray-500'>
        <span className='font-semibold'>Equipment:</span>{" "}
        {video.equipments || "n/a"}
      </p>
    </div>
  );
}
