"use client";
import { useStoreValue } from "@/app/StoreContext";
import { VideoOnDemand } from "@/app/vod/queries/useFetchVideoOnDemands";
import { useSession } from "@/components/custom/login-auth/auth-provider";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { FaPlay } from "react-icons/fa";

export const VideoModal = ({
  video,
  closeModal,
}: {
  video: VideoOnDemand;
  closeModal: () => void;
}) => {
  const { data: session } = useSession();
  const { dispatch } = useStoreValue(state => state.shouldShowLoginModal);
  const [isPlaying, setIsPlaying] = useState(!!session);
  const videoRef = useRef<HTMLVideoElement>(null);
  const { video_url } = video;
  const [error, setError] = useState(false);

  const getYouTubeVideoId = (url: string) => {
    const regExp =
      /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return match && match[2] && match[2].length === 11 ? match[2] : null;
  };

  const isYouTubeVideo =
    video_url?.includes("youtube.com") || video_url?.includes("youtu.be");
  const youtubeVideoId =
    isYouTubeVideo && video_url ? getYouTubeVideoId(video_url) : null;

  useEffect(() => {
    if (videoRef.current && !isYouTubeVideo && video_url) {
      videoRef.current.load();
    }
  }, [video_url, isYouTubeVideo]);

  const handlePlayClick = () => {
    setIsPlaying(true);
    setError(false);
    if (videoRef.current && !isYouTubeVideo) {
      videoRef.current.play().catch(() => {});
    }
  };

  return (
    <>
      <Dialog open onOpenChange={closeModal}>
        <DialogContent
          className='lg:max-w-[800px] mt-4'
          closeButtonClassName='right-0 top-0 lg:right-[-10px] lg:top-[-30px] lg:text-white'
        >
          <div className='h-full overflow-y-auto w-full noselect'>
            <div className='flex flex-col  w-full noselect'>
              <div
                className='w-full h-[480px] relative rounded-lg overflow-hidden cursor-pointer noselect'
                onClick={() => {
                  if (session) {
                    handlePlayClick();
                  }
                }}
              >
                {!isPlaying ? (
                  <>
                    <div className='absolute inset-0 flex items-center justify-center z-20 cursor-pointer'>
                      <FaPlay className='text-white text-5xl' />
                    </div>
                    {error && (
                      <div className='absolute inset-0 flex items-center justify-center z-20 cursor-pointer top-7'>
                        <span className='text-red-500 text-sm font-semibold pt-12'>
                          Unable to play video
                        </span>
                      </div>
                    )}
                    {video.video_thumbnail ? (
                      <Image
                        src={video.video_thumbnail}
                        alt={video.name}
                        fill
                        className='object-cover'
                      />
                    ) : (
                      <div className='w-full h-[480px] bg-gray-300 flex items-center justify-center'>
                        <span className='bg-gray-300 rounded-full block w-full text-center text-xs text-gray-400' />
                      </div>
                    )}
                  </>
                ) : (
                  <>
                    {isYouTubeVideo && youtubeVideoId ? (
                      <iframe
                        src={`https://www.youtube.com/embed/${youtubeVideoId}?autoplay=1&rel=0`}
                        title={video.name}
                        className='w-full h-full'
                        allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
                        allowFullScreen
                        style={{
                          border: "none",
                        }}
                      />
                    ) : (
                      <video
                        ref={videoRef}
                        src={video_url}
                        preload='auto'
                        controls
                        className='w-full h-full'
                        onError={() => {
                          setError(true);
                          setIsPlaying(false);
                        }}
                      />
                    )}
                  </>
                )}
              </div>

              <h1 className='text-2xl font-semibold text-[#009DC4] mt-4 mb-2'>
                {video.name}
              </h1>
              <p className='text-gray-500'>
                <span className='font-semibold'>Instructor:</span>{" "}
                {video.instructor_first_name} {video.instructor_last_name}
              </p>
              <p className='text-gray-500'>
                <span className='font-semibold'>Level:</span> {video.level}
              </p>
              <p className='text-gray-500'>
                <span className='font-semibold'>Category:</span>{" "}
                {video.category_name}
              </p>
              <p className='text-gray-500'>
                <span className='font-semibold'>Equipment:</span>{" "}
                {video.equipments || "n/a"}
              </p>
              <p className='text-gray-500 my-2'>{video.description}</p>

              {!session && (
                <div className='flex justify-center mt-4'>
                  <span
                    className='bg-[#009DC4] text-white px-4 py-2 rounded-md cursor-pointer hover:bg-opacity-80 transition-all duration-300'
                    onClick={() => {
                      closeModal();
                      dispatch(() => ({ shouldShowLoginModal: true }));
                    }}
                  >
                    Sign in to play
                  </span>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
