import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { LoginModal } from "@/components/custom/login-auth/login-modal";
import CategoryDetailsPage from "./components/CategoryDetailsPage";

export default async function CategoryPage({
  params,
}: Readonly<{
  params: {
    orgId: string;
    categoryId: string;
  };
}>) {
  const { configs, appUrls } = await fetchClientConfigs(params.orgId);

  return (
    <div className='p-4'>
      <CategoryDetailsPage />

      <LoginModal
        buttonColor={configs?.embed?.button_color}
        appUrls={appUrls}
        configs={configs}
        orgId={params.orgId}
      />
    </div>
  );
}
