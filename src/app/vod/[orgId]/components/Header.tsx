import { LoginLogoutButton } from "@/components/custom/login-logout-button";

export const Header = ({
  handleSearch,
}: {
  handleSearch: (searchTerm: string) => void;
}) => {
  return (
    <div className='grid grid-cols-1 gap-[30px] mt-3'>
      <div className='flex  flex-col-reverse md:flex-row justify-between mb-3 items-center gap-3 md:gap-0'>
        <input
          type='text'
          placeholder='Search category name'
          className='w-full md:w-full md:max-w-[300px] h-[40px] border border-[#E5E7ED] px-[10px] py-[5px] text-[15px] bg-white placeholder:text-[#999999] active:border-[#bcbdbd] focus:border-[#bcbdbd] outline-none rounded-[15px]'
          onChange={e => handleSearch(e.target.value)}
        />
        <LoginLogoutButton
          ctaLabel='Login to watch videos'
          unauthenticatedCtaClassName='mt-0'
          authenticatedCtaClassName='mt-0'
        />
      </div>
    </div>
  );
};
