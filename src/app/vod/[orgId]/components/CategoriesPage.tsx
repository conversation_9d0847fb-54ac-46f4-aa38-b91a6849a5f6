"use client";

import { useSession } from "@/components/custom/login-auth/auth-provider";
import { useCallback, useEffect, useState } from "react";
import {
  useFetchVoDCategories,
  VoDCategory,
} from "../../queries/useFetchVoDCategories";
import Categories from "./Categories";
import { Header } from "./Header";

export default function CategoriesPage({ orgId }: { orgId: string }) {
  const { data: sessionData } = useSession();
  const [categories, setCategories] = useState<VoDCategory[]>([]);

  const { data, isPending } = useFetchVoDCategories({
    orgId: orgId,
    token: sessionData?.token,
  });

  const handleSearch = useCallback(
    (searchTerm: string) => {
      if (searchTerm) {
        const filteredCategories = data?.filter((category: VoDCategory) =>
          category.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setCategories(filteredCategories ?? []);
      } else {
        setCategories(data ?? []);
      }
    },
    [data]
  );

  useEffect(() => {
    if (data) {
      setCategories(data);
    }
  }, [data]);

  return (
    <div className='w-full max-w-[552px] lg:max-w-[1140px]'>
      <Header handleSearch={handleSearch} />
      <Categories orgId={orgId} categories={categories} isPending={isPending} />
    </div>
  );
}
