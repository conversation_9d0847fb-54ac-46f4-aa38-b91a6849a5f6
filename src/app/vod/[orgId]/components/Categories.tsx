"use client";

import Image from "next/image";
import Link from "next/link";
import { VoDCategory } from "../../queries/useFetchVoDCategories";

const SkeletonLoader = () => {
  return (
    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[30px] justify-items-end'>
      {Array.from({ length: 6 }).map((_, index) => (
        <div
          key={index}
          className='relative w-full w-max-[280px] lg:w-full lg:w-max-[360px] lg:h-[254px] cursor-pointer'
        >
          <div className='absolute top-[10px] right-[10px] z-10 bg-gray-300 animate-pulse w-[60px] h-[25px] rounded-[55px]'></div>

          {/* Image skeleton */}
          <div className='w-full h-[200px] relative overflow-hidden rounded-t-[12px] bg-gray-300 animate-pulse'></div>

          {/* Title skeleton */}
          <div className='bg-gray-300 rounded-b-[12px] h-[50px] animate-pulse'></div>
        </div>
      ))}
    </div>
  );
};

export default function Categories({
  orgId,
  categories,
  isPending,
}: {
  orgId: string;
  categories: VoDCategory[];
  isPending: boolean;
}) {
  if (isPending) {
    return <SkeletonLoader />;
  }

  return (
    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[30px] justify-items-end'>
      {categories?.map(videoCategory => (
        <Link
          href={`/vod/${orgId}/${videoCategory.id}`}
          key={videoCategory.id}
          className='relative w-full w-max-[280px] lg:w-full lg:w-max-[360px] lg:h-[254px] cursor-pointer'
        >
          <span className='absolute top-[10px] right-[10px] z-10 bg-[#323232] text-white px-[18px] py-[5px] rounded-[55px] text-[12px] font-bold bg-opacity-80'>
            {videoCategory.videos_count} video
            {videoCategory.videos_count > 1 ? "s" : ""}
          </span>
          <div className='thumbnail w-full h-[200px] relative overflow-hidden rounded-t-[12px]'>
            {videoCategory.category_image ? (
              <Image
                src={videoCategory.category_image}
                alt={videoCategory.name}
                fill
                className='object-cover object-top rounded-t-[15px] transition-transform duration-300 ease-in-out hover:scale-110'
              />
            ) : (
              <div className='w-full h-[200px] relative overflow-hidden rounded-t-[12px] bg-gray-300 flex items-center justify-center'>
                <span className='bg-gray-300 rounded-full block w-full text-center text-xs text-gray-400'>
                  NO IMAGE
                </span>
              </div>
            )}
          </div>
          <div className='bg-[#00aeef] rounded-b-[12px] text-center text-white py-[10px] text-[16px] font-bold'>
            {videoCategory.name}
          </div>
        </Link>
      ))}
    </div>
  );
}
