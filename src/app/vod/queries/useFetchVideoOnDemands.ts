import { BASE_API_URL_CLIENT } from "@/app/classes/[orgId]/actions/constant";
import { queryOptions, useQuery } from "@tanstack/react-query";

type FetchVideoOnDemandsParams = {
  orgId: string;
  token?: string;
  search?: string;
  categoryId: string | null;
};

export type VideoOnDemand = {
  id: number;
  created: string;
  name: string;
  video_thumbnail: string;
  video_url: string;
  duration_minutes: string;
  level: string | null;
  equipments: string;
  active: number;
  video_passcode: string | null;
  category_id: number;
  category_name: string;
  category_deleted: number;
  gym_name: string;
  instructor_first_name: string;
  instructor_last_name: string;
  instructor_id: number;
  description: string;
};

type FetchVideoOnDemandsResponse = {
  data: VideoOnDemand[];
};

export const fetchVideoOnDemands = async ({
  orgId,
  token,
  categoryId,
  search,
}: FetchVideoOnDemandsParams): Promise<FetchVideoOnDemandsResponse> => {
  const headers: {
    authorization?: string;
  } = {};

  let url = `${BASE_API_URL_CLIENT}/ondemand_videos/unauth?university_id=${orgId}&category_id=${categoryId}`;

  if (token) {
    url = `${BASE_API_URL_CLIENT}/ondemand_videos?category_id=${categoryId}`;
    headers.authorization = `Bearer ${token}`;
  }

  if (search) {
    url += `&search=${search}`;
  }

  const response = await fetch(url, { headers: headers });
  return await response.json();
};

export const useFetchVideoOnDemands = ({
  orgId,
  token,
  categoryId,
  search,
}: FetchVideoOnDemandsParams) => {
  return useQuery({
    queryKey: queryOptions({
      queryKey: [orgId, token, categoryId, search],
    }).queryKey,
    refetchOnWindowFocus: false,
    refetchInterval: false,
    refetchOnMount: true,
    select: data => data?.data,
    queryFn: () => fetchVideoOnDemands({ orgId, token, categoryId, search }),
  });
};
