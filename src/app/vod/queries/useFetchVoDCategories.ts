import { BASE_API_URL_CLIENT } from "@/app/classes/[orgId]/actions/constant";
import { queryOptions, useQuery } from "@tanstack/react-query";

type FetchVoDCategoriesParams = {
  orgId: string;
  token?: string;
};

export type VoDCategory = {
  id: number;
  name: string;
  category_image: string;
  active: 1 | 0;
  videos_count: number;
};

type FetchVoDCategoriesResponse = {
  data: VoDCategory[];
};

export const fetchVoDCategories = async ({
  orgId,
  token,
}: FetchVoDCategoriesParams): Promise<FetchVoDCategoriesResponse> => {
  const headers: {
    authorization?: string;
  } = {};

  let url = `${BASE_API_URL_CLIENT}/ondemand_videos/categories/unauth?university_id=${orgId}`;

  if (token) {
    url = `${BASE_API_URL_CLIENT}/ondemand_videos/categories`;
    headers.authorization = `Bearer ${token}`;
  }

  const response = await fetch(url, { headers: headers });
  return await response.json();
};

export const useFetchVoDCategories = ({
  orgId,
  token,
}: FetchVoDCategoriesParams) => {
  return useQuery({
    queryKey: queryOptions({
      queryKey: [orgId, token],
    }).queryKey,
    refetchOnWindowFocus: false,
    refetchInterval: false,
    refetchOnMount: true,
    select: data => data?.data,
    queryFn: () => fetchVoDCategories({ orgId, token }),
  });
};
