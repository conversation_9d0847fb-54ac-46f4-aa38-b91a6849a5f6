/* eslint-disable import/no-unassigned-import */
import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { FirebaseProvider } from "@/lib/firebase/FirebaseProvider";
import { extractFontDetails, extractIdFromUrl } from "@/lib/utils";
import GlobalStyles from "@/styles/GlobalStyles";
import type { Metadata } from "next";
import { headers } from "next/headers";
import { Toaster } from "sonner";
import "./globals.css";
import Provider from "./providers";

import { NuqsAdapter } from "nuqs/adapters/next/app";

export const metadata: Metadata = {
  title: "upace app",
  description: "upace | Fitness Meet Convenience",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const _headers = headers();

  const url = _headers.get("x-url");
  const params = extractIdFromUrl(url);

  const { embedsConfigs, sgtPurchaseUrl } = await fetchClientConfigs(
    String(params)
  );

  const [fontName, fontUrl] = extractFontDetails(embedsConfigs?.font_style);

  return (
    <html lang='en'>
      <head>
        <link href={fontUrl} rel='stylesheet' />
        <link
          rel='icon'
          href='https://upaceapp.com/img/favicon.png'
          type='image/png'
        />
        {/* <style>
          {`
            :root {
              font-family: "${fontName}", sans-serif;
            }
          `}
        </style> */}
      </head>

      <body style={{ fontFamily: fontName }}>
        <Toaster richColors position='top-center' />
        <GlobalStyles />
        <FirebaseProvider>
          <NuqsAdapter>
            <Provider
              initialState={{
                ...embedsConfigs,
                fontName,
                fontUrl,
                sgtPurchaseUrl,
              }}
            >
              {children}
            </Provider>
          </NuqsAdapter>
        </FirebaseProvider>
      </body>
    </html>
  );
}
