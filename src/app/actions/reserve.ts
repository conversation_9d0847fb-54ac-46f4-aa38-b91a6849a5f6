"use client";

import { getSession } from "@/components/custom/login-auth/auth-provider";
import { FirebaseEvent, logEvent } from "@/lib/firebase/config";
import { format } from "date-fns";
import { BASE_API_URL_CLIENT } from "../classes/[orgId]/actions/constant";

interface ReservationEventData {
  class_id: string;
  date: string;
  is_virtual: boolean;
  first_name?: string;
  last_name?: string;
}

const logReservationEvent = (
  eventType: FirebaseEvent.CLASS_RESERVED | FirebaseEvent.CLASS_RESERVED_FAILED,
  data: ReservationEventData
) => {
  logEvent(eventType, {
    class_id: data.class_id,
    date: format(new Date(data.date), "MM/dd/yyyy"),
    isVirtual: data.is_virtual ? "Yes" : "No",
    user: `${data.first_name} ${data.last_name}`.trim(),
  });
};

export const reserveAction = async (data: any) => {
  const session = await getSession();

  const headers: { "Content-Type": string; authorization?: string } = {
    "Content-Type": "application/json",
  };

  if (session) {
    headers.authorization = `Bearer ${session?.token}`;
  }

  try {
    const response = await fetch(`${BASE_API_URL_CLIENT}/reserve`, {
      headers,
      method: "POST",
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (result?.success) {
      logReservationEvent(FirebaseEvent.CLASS_RESERVED, {
        ...data,
        first_name: session?.first_name,
        last_name: session?.last_name,
      });

      return await result;
    }

    logReservationEvent(FirebaseEvent.CLASS_RESERVED_FAILED, {
      ...data,
      first_name: session?.first_name,
      last_name: session?.last_name,
    });

    throw new Error(`${result.message}`);
  } catch (err) {
    logReservationEvent(FirebaseEvent.CLASS_RESERVED_FAILED, {
      ...data,
      first_name: session?.first_name,
      last_name: session?.last_name,
    });

    throw new Error(`${err}`);
  }
};
