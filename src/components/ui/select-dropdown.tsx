import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@radix-ui/react-select";
import { ChevronDown } from "lucide-react";

interface Option {
  value: string;
  label: string;
}

interface SelectDropdownProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  className?: string;
  triggerContent?: React.ReactNode;
  placeholder?: string;
  position?: "popper" | "item-aligned";
}

export const SelectDropdown = ({
  options,
  value,
  onChange,
  className = "",
  triggerContent,
  placeholder = "Select an option",
  position = "popper",
}: SelectDropdownProps) => {
  return (
    <div
      className={`flex flex-col gap-2 z-50 border-[#DAE0E8] border  ${className}`}
    >
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger className='text-sm p-2'>
          <div className='flex justify-between items-center w-full h-full'>
            {triggerContent || (
              <span>
                {options.find(opt => opt.value === value)?.label || placeholder}
              </span>
            )}
            <ChevronDown className='w-4 h-4' />
          </div>
        </SelectTrigger>
        <SelectContent
          position={position}
          className='w-[var(--radix-select-trigger-width)] min-w-[var(--radix-select-trigger-width)] bg-white rounded-md shadow-lg border border-[#DAE0E8] overflow-hidden'
        >
          {options.map(option => (
            <SelectItem
              key={option.value}
              value={option.value}
              className='text-sm text-gray-600 p-2 cursor-pointer hover:bg-gray-50'
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};
