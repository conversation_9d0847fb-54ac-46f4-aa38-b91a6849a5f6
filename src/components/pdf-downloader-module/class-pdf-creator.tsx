/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/ban-ts-comment */
"use client";

import {
  DataTableCell,
  Table,
  TableBody,
  TableCell,
  TableHeader,
} from "@propra/react-pdf-table";
import {
  Document,
  Image,
  Page,
  StyleSheet,
  Text,
  View,
} from "@react-pdf/renderer";

import { ClassDetailsResponse } from "@/app/classes/[orgId]/types";
import { formatTime, obtainDateFrame } from "@/app/classes/[orgId]/utils";
import { PDFQRCode } from "./pdf-qrcode";

// import { ScanQRCode } from "../custom/scan-qr-code";

const CalendarView = <T extends ClassDetailsResponse>({
  row,
  column,
}: {
  row: T;
  column: string;
}) => {
  if (!row.days_of_week?.includes(column)) {
    return <View wrap></View>;
  }
  return (
    <View wrap style={styles.calendarView}>
      <Text style={styles.calendarViewText}>
        {obtainDateFrame(row.start_time, row.end_time)}
      </Text>
      <Text wrap style={styles.calendarViewName}>
        {row.name}
      </Text>
      <Text style={styles.calendarViewText}>{row.instructor}</Text>
      <Text style={styles.calendarViewText}>{row.room_name}</Text>
    </View>
  );
};

const columns = [
  { id: "Sun", value: "SUNDAY" },
  { id: "Mon", value: "MONDAY" },
  { id: "Tue", value: "TUESDAY" },
  { id: "Wed", value: "WEDNESDAY" },
  { id: "Thu", value: "THURSDAY" },
  { id: "Fri", value: "FRIDAY" },
  { id: "Sat", value: "SATURDAY" },
] as const;

export const ClassPDFCreator = <T,>({
  data,
  clientName,
  activityType,
  weekPeriod,
  configs,
  logo,
  header,
  category,
  iosUrl,
  androidUrl,
}: {
  data: T[];
  clientName?: string;
  activityType?: string;
  weekPeriod?: string;
  configs?: Record<string, string>;
  logo?: string;
  header?: string;
  category?: string;
  iosUrl?: string;
  androidUrl?: string;
}) => {
  return (
    <Document>
      <Page size='SRA2'>
        <View
          style={{ margin: "2% auto", width: "90%", fontFamily: configs?.font }}
        >
          <View
            style={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-between",
              marginTop: "2%",
              marginBottom: "1%",
            }}
          >
            <View
              style={{
                display: "flex",
                flexDirection: "row",
                gap: 5,
                marginBottom: 10,
              }}
            >
              {logo && (
                <View style={{ width: 100, height: 100 }}>
                  <Image src={logo} style={{ objectFit: "contain" }} />
                </View>
              )}
              <View style={{ marginLeft: 50, marginTop: 50 }}>
                <Text style={{ fontSize: 25, fontWeight: "ultrabold" }}>
                  {category ? `${category} Class Schedule` : "SCHEDULE"}
                </Text>
                <Text
                  style={{ fontSize: 12 }}
                >{`${clientName}: ${activityType ?? "All Locations"}`}</Text>
                <Text style={{ fontSize: 12 }}>{`As of ${weekPeriod}`}</Text>
              </View>
            </View>

            {header && (
              <View
                style={{
                  height: 50,
                  marginTop: 50,
                  width: 300,
                  textAlign: "right",
                }}
              >
                <Text style={{ fontSize: 13 }}>{header}</Text>
              </View>
            )}
          </View>

          <Table data={data}>
            <TableHeader
              includeTopBorder={false}
              includeBottomBorder={false}
              includeLeftBorder={false}
              includeRightBorder={false}
              textAlign='center'
            >
              <TableCell
                weighting={0.5}
                style={{ backgroundColor: configs?.accent_color }}
              />
              {columns.map(column => (
                <TableCell
                  //@ts-expect-error
                  key={column.id}
                  textAlign='center'
                  style={{
                    ...styles.tableCell,
                    backgroundColor: configs?.accent_color,
                    fontWeight: "bolder",
                  }}
                >
                  {column.value}
                </TableCell>
              ))}
            </TableHeader>
            <TableBody
              includeLeftBorder={false}
              includeBottomBorder={false}
              includeTopBorder={false}
              includeRightBorder={false}
              renderTopBorder={false}
              evenRowColor='#F1F1F1'
              oddRowColor='white'
              even
            >
              <DataTableCell
                textAlign='center'
                weighting={0.5}
                getContent={(r: ClassDetailsResponse) => (
                  <View
                    style={{
                      height: 100,
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Text>{formatTime(r.start_time)}</Text>
                  </View>
                )}
                style={{
                  ...styles.dataTableCell,
                  backgroundColor: configs?.accent_color,
                }}
              />

              {columns.map(column => (
                <DataTableCell
                  //@ts-expect-error
                  key={column.id}
                  style={{
                    borderWidth: 1,
                    borderStyle: "solid",
                    borderColor: "white",
                    justifyCenter: "start",
                    alignItems: "start",
                  }}
                  getContent={(r: ClassDetailsResponse) => (
                    <CalendarView row={r} column={column.id.toLowerCase()} />
                  )}
                />
              ))}
            </TableBody>
          </Table>

          <View style={{ textAlign: "center", marginTop: 10 }}>
            <Text style={{ fontSize: 30 }}>
              Scan the QR code to download the app
            </Text>
            <View
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
                textAlign: "center",
                gap: 10,
              }}
            >
              <PDFQRCode label={"iPhone"} url={String(iosUrl)} />
              <PDFQRCode label={"Android"} url={String(androidUrl)} />
            </View>
          </View>
        </View>
      </Page>
    </Document>
  );
};

// Define your styles
const styles = StyleSheet.create({
  calendarView: {
    borderRadius: 5,
    marginTop: 5,
    height: 100,
    marginLeft: 10,
  },
  calendarViewText: {
    fontSize: 13,
    paddingBottom: 5,
  },
  calendarViewName: {
    fontSize: 16,
    fontWeight: "black",
    whiteSpace: "nowrap",
    paddingBottom: 5,
  },
  tableCell: {
    backgroundColor: "#5BBACB",
    color: "white",
    borderWidth: 1,
    borderStyle: "solid",
    borderColor: "white",
    height: 30,
    justifyContent: "center",
    alignItems: "center",
    fontSize: 14,
  },
  dataTableCell: {
    backgroundColor: "#5BBACB",
    color: "white",
    display: "flex",
    flexDirection: "column",
    justifyCenter: "center",
    borderWidth: 1,
    borderStyle: "solid",
    borderColor: "white",
    alignItems: "center",
  },
});
