/* eslint-disable jsx-a11y/alt-text */
import { Image, Text, View } from "@react-pdf/renderer";

export const Header = ({
  logo,
  clientName,
  activityType,
  weekPeriod,
  clientBackground,
  accentColor,
}: {
  logo?: string;
  header?: string;
  category?: string;
  clientName?: string;
  activityType?: string;
  weekPeriod?: string;
  clientBackground?: string;
  accentColor?: string;
}) => {
  return (
    <View
      wrap
      style={{
        position: "relative",
        height: 150,
        marginTop: "2%",
        marginBottom: "1%",
        overflow: "hidden",
        borderRadius: 1,
        backgroundColor: clientBackground ? undefined : accentColor,
      }}
    >
      {clientBackground && (
        <Image
          style={{
            width: "100%",
            objectFit: "cover",
            position: "absolute",
            zIndex: 0,
            top: 0,
            opacity: 0.4,
          }}
          src={clientBackground}
        />
      )}

      <View
        style={{
          display: "flex",
          flexDirection: "row",
          marginTop: 25,
          marginLeft: 25,
          textAlign: "center",
          padding: 10,
          justifyContent: "space-between",
          position: "relative",
          zIndex: 1, // Text container
        }}
      >
        <View
          style={{
            position: "relative",
            zIndex: 2,
          }}
        >
          <Text style={{ fontSize: 25, fontWeight: "black" }}>
            {`${clientName} Class Schedule`}
          </Text>
          <Text style={{ fontSize: 12, marginTop: 5 }}>{activityType}</Text>
          <Text
            style={{ fontSize: 12, marginTop: 20 }}
          >{`As of ${weekPeriod}`}</Text>
        </View>
        {logo && (
          <View style={{ marginRight: 80 }}>
            <Image
              src={logo}
              style={{
                width: 50,
                objectFit: "cover",
                position: "absolute",
                zIndex: 2,
                color: "white",
              }}
            />
          </View>
        )}
      </View>
    </View>
  );
};
