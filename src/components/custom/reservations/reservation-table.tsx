/* eslint-disable @typescript-eslint/ban-ts-comment */
"use client";
import { Fragment, ReactNode, useMemo } from "react";
import { groupBy, isEmpty } from "lodash/fp";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "@/components/ui/table";

import { StyledTableHeader } from "@/components/data-table/data-table";
import { ClassDetailsResponse } from "../../../app/classes/[orgId]/types";
import { ReservationSkeleton } from "./reservation-loader";

export interface Column {
  name: string;
  renderCell: (item: ClassDetailsResponse) => ReactNode | string;
}

interface ReservationTableProps {
  isPending: boolean;
  data: ClassDetailsResponse[];
  formatReservationDate: (date: string) => string;
  columns: Column[];
  color?: string;
}
export function ReservationTable({
  isPending,
  data,
  formatReservationDate,
  columns,
  color,
}: ReservationTableProps) {
  const allData = useMemo(
    () => groupBy(rec => formatReservationDate(rec.start_time), data),
    [data, formatReservationDate]
  );

  return (
    <div className='overflow-auto max-h-[500px]'>
      <Table>
        <StyledTableHeader color={color}>
          <TableRow>
            {columns.map(columnName => (
              <TableHead key={columnName.name}>{columnName.name}</TableHead>
            ))}
          </TableRow>
        </StyledTableHeader>

        <TableBody>
          {isPending && <ReservationSkeleton columns={columns} />}

          {!isPending && isEmpty(allData) && (
            <TableRow>
              <TableCell
                colSpan={columns.length}
                className='text-center text-lg mt-4'
              >
                You currently do not have any active reservations, please go
                back to the previous screen to make a reservations.
              </TableCell>
            </TableRow>
          )}

          {Object.entries(allData).map(([day, items]) => (
            // @ts-expect-error
            <Fragment key={day}>
              <TableRow className='hover:bg-white'>
                <TableCell colSpan={5} className='font-bold mb-4 pt-4 pb-4'>
                  {day?.toUpperCase()}
                </TableCell>
              </TableRow>
              {items.map(item => (
                <TableRow key={item.id}>
                  {columns.map(column => (
                    <TableCell key={column.name}>
                      {column.renderCell(item)}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </Fragment>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
