"use client";

import { obtainDateFrame } from "@/app/classes/[orgId]/utils";
import { format, parseISO } from "date-fns";
import { styled } from "styled-components";
import { ClassDetailsResponse } from "../../../app/classes/[orgId]/types";

export const DetailsCell = ({ data }: { data: ClassDetailsResponse }) => {
  const { gym_name, start_time, end_time, name } = data;

  return (
    <div className='rounded-md bg-white mb-8'>
      <p className='font-extrabold whitespace'>
        {obtainDateFrame(start_time, end_time)}
      </p>
      <p className='font-normal mt-2 whitespace'>{name}</p>
      <p className='font-normal mt-2 whitespace'>
        {`${data?.instructor_first_name || ""} ${data?.instructor_last_name || ""}`}
      </p>
      <p className='font-normal mt-2 whitespace'>{gym_name}</p>
    </div>
  );
};

export const StyledCell = styled.div<{ color?: string }>`
  color: ${({ color }) => color};
`;

export const formatReservationDate = (date: string) => {
  return format(parseISO(date.split(" ")[0] ?? ""), "EEEE, MMMM d");
};
