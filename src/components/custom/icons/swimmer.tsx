export const Swimmer = ({ className }: { className?: string }) => {
  return (
    <svg
      width='20'
      height='21'
      viewBox='0 0 20 21'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      className={className}
    >
      <path
        d='M16.3889 6.1C16.9097 6.1 17.3611 6.375 17.6042 6.7875C17.8472 7.23438 17.8472 7.75 17.6042 8.1625C17.3611 8.60938 16.9097 8.85 16.3889 8.85C15.9028 8.85 15.4514 8.60938 15.2083 8.1625C14.9653 7.75 14.9653 7.23438 15.2083 6.7875C15.4514 6.375 15.9028 6.1 16.3889 6.1ZM16.3889 9.95C17.2917 9.95 18.125 9.50313 18.5764 8.7125C19.0278 7.95625 19.0278 7.02813 18.5764 6.2375C18.125 5.48125 17.2917 5 16.3889 5C15.5208 5 14.6875 5.48125 14.2361 6.2375C13.7847 7.02813 13.7847 7.95625 14.2361 8.7125C14.6875 9.50313 15.5208 9.95 16.3889 9.95ZM15.3819 14.1781C14.7917 14.5906 14.0625 14.9 13.3333 14.9C12.6389 14.9 11.9097 14.5563 11.3194 14.1781C10.9722 13.9719 10.6944 13.6969 10.4167 13.4219C10.2083 13.2156 9.82639 13.2156 9.61806 13.4219C9.34028 13.6969 9.02778 13.9719 8.71528 14.1781C8.125 14.5906 7.39583 14.9 6.66667 14.9C5.97222 14.9 5.24305 14.5563 4.65278 14.1781C4.30555 13.9375 4.02778 13.6969 3.75 13.4219C3.54167 13.2156 3.15972 13.2156 2.95139 13.4219C2.67361 13.6969 2.36111 13.9375 2.04861 14.1781C1.59722 14.4875 1.07639 14.7625 0.520832 14.8656C0.243055 14.9344 0 15.1406 0 15.4156C0 15.725 0.277777 16.0344 0.625 15.9656C1.42361 15.8281 2.15278 15.45 2.67361 15.1062C2.95139 14.9 3.15972 14.7281 3.33333 14.5563C3.54167 14.7281 3.75 14.9 4.02778 15.1062C4.6875 15.5188 5.625 16 6.66667 16C7.74306 16 8.68056 15.5188 9.34028 15.1062C9.61806 14.9 9.82639 14.7281 10 14.5563C10.2083 14.7281 10.4167 14.9 10.6944 15.1062C11.3542 15.5188 12.2917 16 13.3333 16C14.4097 16 15.3472 15.5188 16.0069 15.1062C16.2847 14.9 16.4931 14.7281 16.6667 14.5563C16.875 14.7281 17.0833 14.9 17.3611 15.1062C17.8819 15.45 18.6111 15.8281 19.4097 15.9656C19.7222 16.0344 20 15.725 20 15.4156C20 15.1406 19.7917 14.9344 19.5139 14.8656C18.9583 14.7625 18.4375 14.4875 17.9861 14.1781C17.6389 13.9719 17.3611 13.6969 17.0833 13.4219C16.875 13.2156 16.4931 13.2156 16.2847 13.4219C16.0069 13.6969 15.6944 13.9719 15.3819 14.1781ZM11.0417 8.05938L11.1111 7.99063C12.1528 9.09063 12.7778 10.5687 12.7778 12.15V12.6656C12.9861 12.7 13.1597 12.7344 13.3333 12.7344C13.5417 12.7344 13.7153 12.7 13.8889 12.6656V12.15C13.8889 8.23125 10.6597 5 6.66667 5H3.88889C3.61111 5 3.33333 5.275 3.33333 5.55C3.33333 5.85938 3.61111 6.1 3.88889 6.1H6.66667C8.02083 6.1 9.27083 6.54688 10.2778 7.26875L4.61805 12.0125V12.0469C4.96528 12.2531 5.3125 12.425 5.69444 12.5625L11.0417 8.05938Z'
        fill='black'
      />
    </svg>
  );
};
