export const DoorClosed = ({ className }: { className?: string }) => {
  return (
    <svg
      width='20'
      height='21'
      viewBox='0 0 20 21'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      className={className}
    >
      <path
        opacity='0.99'
        d='M2.5 18.3333C2.22386 18.3333 2 18.5572 2 18.8333C2 19.1095 2.22386 19.3333 2.5 19.3333V18.3333ZM17.5 19.3333C17.7761 19.3333 18 19.1095 18 18.8333C18 18.5572 17.7761 18.3333 17.5 18.3333V19.3333ZM4.5 18.8333C4.5 19.1095 4.72386 19.3333 5 19.3333C5.27614 19.3333 5.5 19.1095 5.5 18.8333H4.5ZM5.55556 2.16667V2.66667V2.16667ZM14.5 18.8333C14.5 19.1095 14.7239 19.3333 15 19.3333C15.2761 19.3333 15.5 19.1095 15.5 18.8333H14.5ZM12.8333 10.5C12.8333 10.6841 12.6841 10.8333 12.5 10.8333V11.8333C13.2364 11.8333 13.8333 11.2364 13.8333 10.5H12.8333ZM12.5 10.8333C12.3159 10.8333 12.1667 10.6841 12.1667 10.5H11.1667C11.1667 11.2364 11.7636 11.8333 12.5 11.8333V10.8333ZM12.1667 10.5C12.1667 10.3159 12.3159 10.1667 12.5 10.1667V9.16667C11.7636 9.16667 11.1667 9.76363 11.1667 10.5H12.1667ZM12.5 10.1667C12.6841 10.1667 12.8333 10.3159 12.8333 10.5H13.8333C13.8333 9.76363 13.2364 9.16667 12.5 9.16667V10.1667ZM2.5 19.3333H17.5V18.3333H2.5V19.3333ZM5.5 18.8333V2.86112H4.5V18.8333H5.5ZM5.5 2.86112C5.5 2.77789 5.52737 2.71464 5.55315 2.68242L4.77228 2.05772C4.58969 2.28597 4.5 2.57599 4.5 2.86112H5.5ZM5.55315 2.68242C5.55903 2.67506 5.56402 2.6703 5.56747 2.66742C5.57091 2.66455 5.57283 2.66355 5.57285 2.66355C5.57287 2.66354 5.57245 2.66375 5.57159 2.66408C5.57073 2.66441 5.56949 2.66482 5.56787 2.66523C5.56452 2.66606 5.56029 2.66667 5.55556 2.66667V1.66667C5.23501 1.66667 4.9572 1.82658 4.77228 2.05772L5.55315 2.68242ZM5.55556 2.66667H14.4444V1.66667H5.55556V2.66667ZM14.4444 2.66667C14.4397 2.66667 14.4355 2.66606 14.4321 2.66523C14.4305 2.66482 14.4293 2.66441 14.4284 2.66408C14.4276 2.66375 14.4271 2.66354 14.4272 2.66355C14.4272 2.66355 14.4291 2.66455 14.4325 2.66742C14.436 2.6703 14.441 2.67506 14.4468 2.68242L15.2277 2.05772C15.0428 1.82658 14.765 1.66667 14.4444 1.66667V2.66667ZM14.4468 2.68242C14.4726 2.71464 14.5 2.77788 14.5 2.86112H15.5C15.5 2.57599 15.4103 2.28597 15.2277 2.05772L14.4468 2.68242ZM14.5 2.86112V18.8333H15.5V2.86112H14.5Z'
        fill='#272627'
      />
    </svg>
  );
};
