"use client";

import React, { useMemo } from "react";
// import Image from "next/image";
import { ClassDetailsResponse } from "@/app/classes/[orgId]/types";
import { obtainDateFrame } from "@/app/classes/[orgId]/utils";
import { DAYS_OF_WEEK } from "@/common/common.utils";
import { ScanQRCode } from "../scan-qr-code";
import { groupClassesByDaysOfWeek } from "./utils";

interface DaySchedule {
  [key: string]: ClassDetailsResponse[];
}

const Notice = ({
  iosUrl,
  androidUrl,
  clientId,
}: {
  iosUrl?: string;
  androidUrl?: string;
  clientId?: string;
}) => {
  return (
    <div className='flex justify-between gap-2'>
      {!["99", "65"].includes(clientId ?? "") && (
        <p className='mt-12'>
          <span className='font-bold mb-2 pr-2'>NOTES:</span>
          Cancellations must be made 60 minutes prior to class start time. Your
          spot will be forfeited if you do not arrive within 10 minutes of the
          class start time. If you do not cancel and do not show up for 3
          classes in a month, your reservation privileges will be suspended.
        </p>
      )}

      <div className='text-sm'>
        <ScanQRCode androidUrl={String(androidUrl)} iosUrl={String(iosUrl)} />
      </div>
    </div>
  );
};

const Footer = ({ header }: { header?: string }) =>
  header ? (
    <div className='bg-blue-600 text-white  mt-4'>
      <p className='text-sm p-2'>PLEASE NOTE: {header}</p>
    </div>
  ) : null;

const Header = ({
  title,
  branchName,
  date,
}: {
  title?: string;
  branchName?: string;
  date?: string;
  logo?: string;
  background?: string;
  accentColor?: string;
}) => {
  return (
    <div className='flex justify-between rounded-lg relative'>
      <div className='z-10 pl-4 font-bold pt-2 pb-2 relative'>
        <h1 className='text-lg font-bold mb-1'>{`${title?.toUpperCase()} CLASS SCHEDULE`}</h1>
        <h2 className='text-lg mb-1'>{branchName}</h2>
        <p className='pt-1 pb-1'>As of {date}</p>
      </div>

      {/* {logo && (
        <Image
          src={logo}
          width={0}
          height={0}
          alt='logo'
          quality={100}
          style={{
            width: "10%",
            height: "10%",
            marginTop: "5%",
            objectFit: "contain",
            position: "relative",
            zIndex: 2000,
            // backgroundColor: "transparent",
            right: "5%",
          }}
        />
      )} */}
    </div>
  );
};

interface ClassScheduleProps {
  days: string[];
  groupedData: DaySchedule;
  accentColor?: string;
}

const ClassItem = ({
  end_time,
  start_time,
  instructor,
  name,
  room_name,
  date_info,
  is_class_subbed,
  subbing_instructor,
  column,
}: ClassDetailsResponse & { column: string }) => {
  const dateInfo = date_info?.find(day => day.dow === column);

  return (
    <div
      className={`pt-2 px-2 pb-4 border-b border-gray-300 last:border-b-0 text-xs ${dateInfo?.cancelled ? "bg-red-100 text-red-500" : ""}`}
    >
      <p
        style={{
          pageBreakInside: "avoid",
          pageBreakBefore: "avoid",
          pageBreakAfter: "avoid",
        }}
        className={`font-bold ${dateInfo?.cancelled ? "text-red-500" : ""}`}
      >
        {obtainDateFrame(start_time, end_time)}
      </p>
      <p
        style={{
          pageBreakInside: "avoid",
          pageBreakBefore: "avoid",
          pageBreakAfter: "avoid",
        }}
        className={dateInfo?.cancelled ? "text-red-500" : ""}
      >
        {name}
      </p>
      {is_class_subbed ? (
        <p
          style={{
            pageBreakInside: "avoid",
            pageBreakBefore: "avoid",
            pageBreakAfter: "avoid",
          }}
          className={dateInfo?.cancelled ? "text-red-500" : ""}
        >{`*${subbing_instructor}`}</p>
      ) : (
        <p
          style={{
            pageBreakInside: "avoid",
            pageBreakBefore: "avoid",
            pageBreakAfter: "avoid",
          }}
          className={`font-bold ${dateInfo?.cancelled ? "text-red-500" : ""}`}
        >
          {instructor}
        </p>
      )}
      <p
        style={{
          pageBreakInside: "avoid",
          pageBreakBefore: "avoid",
          pageBreakAfter: "avoid",
        }}
        className={dateInfo?.cancelled ? "text-red-500" : ""}
      >
        {room_name}
      </p>
      {dateInfo?.cancelled && (
        <div
          className='flex justify-end mt-3'
          style={{
            pageBreakInside: "avoid",
            pageBreakBefore: "avoid",
            pageBreakAfter: "avoid",
          }}
        >
          <p className='text-red-500 font-bold'>CANCELLED</p>
        </div>
      )}
    </div>
  );
};

const ClassScheduleDetails: React.FC<ClassScheduleProps> = ({
  days,
  groupedData,
  accentColor,
}) => (
  <div className='border-t border-gray-300 rounded overflow-hidden mt-0'>
    <div
      style={{ backgroundColor: accentColor }}
      className='grid grid-cols-7 text-white'
    >
      {days.map(day => (
        <span
          key={day}
          className='p-1 pt-0 text-xs pb-3 font-bold block border-r border-white last:border-r-0 text-center'
          // className='p-1 text-xs pb-2 font-bold border-r border-white last:border-r-0 text-center'
        >
          {day.toUpperCase()}
        </span>
      ))}
    </div>
    <div className='grid grid-cols-7'>
      {days.map((day, dayIndex) => (
        <div
          key={day}
          className={`${dayIndex === 0 ? "border-l-0" : dayIndex === 6 ? "border-r-0" : "border-x"} border-gray-300`}
        >
          {groupedData[day]?.map((classItem, index) => (
            //@ts-expect-error expect key
            <ClassItem key={index} {...classItem} column={day} />
          ))}
        </div>
      ))}
    </div>
  </div>
);

const days = DAYS_OF_WEEK.map(day => day.value);

const ClassPDFCreator = ({
  data,
  clientName,
  activityType,
  weekPeriod,
  logo,
  configs,
  iosUrl,
  androidUrl,
  clientBackground,
  clientId,
  header,
}: {
  data: ClassDetailsResponse[];
  clientName?: string;
  activityType?: string;
  weekPeriod?: string;
  configs?: Record<string, string>;
  logo?: string;
  header?: string;
  category?: string;
  iosUrl?: string;
  androidUrl?: string;
  clientBackground?: string;
  clientId?: string;
}) => {
  const groupedData = useMemo(() => groupClassesByDaysOfWeek(data), [data]);

  return (
    <div className='max-w-7xl mx-auto p-2 avoid-break'>
      <Header
        background={clientBackground}
        title={clientName}
        branchName={activityType}
        date={weekPeriod}
        logo={logo}
        accentColor={configs?.accent_color}
      />
      <ClassScheduleDetails
        days={days}
        groupedData={groupedData}
        accentColor={configs?.accent_color}
      />

      <div className='pt-2 avoid-break'>
        <hr className='border-gray-300' />
        <p>*Sub Instructor</p>
      </div>

      <Notice clientId={clientId} iosUrl={iosUrl} androidUrl={androidUrl} />
      <Footer header={header} />
    </div>
  );
};

export { ClassPDFCreator };
