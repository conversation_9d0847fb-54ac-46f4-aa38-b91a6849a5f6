import { ReactNode } from "react";

export interface PDFOptions {
  margin: number | [number, number, number, number];
  filename: string;
  image: {
    type: string;
    quality: number;
  };
  html2canvas: {
    scale: number;
    dpi?: number;
    letterRendering?: boolean;
    useCORS?: boolean;
    allowTaint?: boolean;
    [key: string]: unknown;
  };
  jsPDF: {
    unit: "pt" | "mm" | "cm" | "in" | "px" | "pc" | "em" | "ex";
    format: string | [number, number];
    orientation: "portrait" | "landscape";
    [key: string]: unknown;
  };
  pagebreak: Record<string, string | number>;
  enableLink: boolean;
}

export interface PDFDownloadButtonProps {
  /** The content to be converted to PDF */
  content: ReactNode;
  /** The name of the downloaded file (default: 'download.pdf') */
  filename?: string;
  /** Additional CSS classes for the button */
  className?: string;
  /** Custom PDF generation options */
  pdfOptions?: Partial<PDFOptions>;
  isLoading?: boolean;

  disabled?: boolean;
}
