/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-ignore html2pdf does not support types
import { ClassDetailsResponse } from "@/app/classes/[orgId]/types";
import html2pdf from "html3pdf";
import { forEach, uniq } from "lodash/fp";
import { PDFOptions } from "./types";

export const defaultPDFOptions: PDFOptions = {
  margin: [0.5, 0.5, 0.5, 0.5],
  filename: "class-schedule.pdf",
  image: {
    type: "jpeg",
    quality: 0.98,
  },
  html2canvas: {
    scale: 2,
    dpi: 192,
    letterRendering: true,
    useCORS: true,
    allowTaint: true,
  },
  jsPDF: {
    unit: "in",
    format: "letter",
    orientation: "landscape",
  },
  pagebreak: {
    mode: "legacy",
    before: ".page-break-before",
    after: ".page-break-after",
    avoid: ".avoid-break",
  },
  enableLink: true,
};

export const generatePDF = async (
  element: HTMLElement,
  options: Partial<PDFOptions> = {}
): Promise<void> => {
  // Only run in browser environment
  if (typeof window === "undefined") {
    return;
  }

  const mergedOptions = {
    ...defaultPDFOptions,
    ...options,
  };

  try {
    await html2pdf().set(mergedOptions).from(element).save();
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw error;
  }
};

export const groupClassesByDaysOfWeek = (classes: ClassDetailsResponse[]) => {
  const groups = {
    mon: [],
    tue: [],
    wed: [],
    thu: [],
    fri: [],
    sat: [],
    sun: [],
  };

  // Process each class
  forEach(classItem => {
    const uniqueDays = uniq(
      classItem?.days_of_week?.map(day => day?.toLowerCase())
    );

    // Add class to each unique day's array
    forEach(day => {
      if (day in groups) {
        // @ts-expect-error test error
        groups?.[day]?.push(classItem);
      }
    }, uniqueDays);
  }, classes);

  return groups;
};
