"use client";

import { useRef, useCallback, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { DownloadCloud, Loader2 } from "lucide-react";
import { generatePDF } from "./utils";
import { PDFDownloadButtonProps } from "./types";

export const PDFDownloadButton: React.FC<PDFDownloadButtonProps> = ({
  content,
  filename = "download.pdf",
  className = "",
  pdfOptions = {},
  isLoading,
  disabled,
}) => {
  const contentRef = useRef<HTMLDivElement>(null);

  const [loading, setLoading] = useState(false);

  const handleDownload = useCallback(async () => {
    if (!contentRef.current) {
      return;
    }
    setLoading(true);

    try {
      await generatePDF(contentRef.current, {
        ...pdfOptions,
        filename,
      });
      setLoading(false);
    } catch (error) {
      setLoading(false);
      // Error is already logged in generatePDF
    } finally {
      setLoading(false);
    }
  }, [filename, pdfOptions]);

  return (
    <>
      <Button
        disabled={disabled || isLoading || loading}
        onClick={handleDownload}
        className={`inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors ${className}`}
      >
        {isLoading || loading ? (
          <Loader2 className='mr-2 h-4 w-4 animate-spin' />
        ) : (
          <DownloadCloud
            className='mr-2 h-4 w-4 hover:bg-blue-600'
            color='white'
          />
        )}

        {loading ? (
          <span>Generating PDF...</span>
        ) : (
          <span>Download schedule</span>
        )}
      </Button>
      <div className='hidden'>
        <div ref={contentRef}>{content}</div>
      </div>
    </>
  );
};
