import { cn } from "@/lib/utils";
import { Badge } from "../ui/badge";

export enum DefaultStatus {
  RESERVED = "current_user_reservation",
  FACILITY_CLOSED = "facility_closed",
  CANCELLED = "cancelled",
  FULL = "is_full",
  NEW = "is_new",
}

export const statusTextMap = {
  [DefaultStatus.RESERVED]: "RESERVED",
  [DefaultStatus.CANCELLED]: "CANCELLED",
  [DefaultStatus.FACILITY_CLOSED]: "CANCELLED",
  [DefaultStatus.FULL]: "FULL",
  [DefaultStatus.NEW]: "NEW",
};

export const statusColorMap = {
  [DefaultStatus.FULL || "Full"]: "bg-lightOrange-500",
  [DefaultStatus.NEW]: "bg-emerald-300",
  [DefaultStatus.CANCELLED]: "bg-red-500",
  [DefaultStatus.RESERVED]: "bg-lightBlue-300",
  [DefaultStatus.FACILITY_CLOSED]: "bg-red-400",
};

export const defaultGetStatus = (status?: Record<string, string>) => {
  const statusKeys = Object.values(DefaultStatus);
  return statusKeys.find(key => status?.[key]);
};

export const ColumnBadge = ({
  status,
  getStatus = defaultGetStatus,
  className,
  displayClassName,
}: {
  status?: Record<string, string>;
  getStatus?: (status?: Record<string, string>) => DefaultStatus | undefined;
  className?: string;
  displayClassName?: string;
}) => {
  const foundStatus = getStatus(status);

  if (!foundStatus) {
    return null;
  }

  const badgeInfo = {
    text: statusTextMap[foundStatus],
    color: statusColorMap[foundStatus],
  };

  return (
    <Badge
      className={cn(
        `flex items-center justify-center w-16  hover:${badgeInfo.color} hover:text-white`,
        displayClassName ? "w-max rounded-md" : "",
        className,
        badgeInfo.color
      )}
      style={{ fontSize: 8, fontWeight: "bold" }}
    >
      <p className={displayClassName ? "text-2xl p-4 whitespace-nowrap" : ""}>
        {badgeInfo.text}
      </p>
    </Badge>
  );
};
