"use client";

import { useStoreValue } from "@/app/StoreContext";
import { Button } from "@/components/ui/button";
import { LogIn } from "lucide-react";
import { useSession } from "./login-auth/auth-provider";
import { UserNavigationMenu } from "./user-navigation";

export const LoginLogoutButton = ({
  ctaLabel = "Login to make reservations",
  unauthenticatedCtaClassName = "mt-4",
  authenticatedCtaClassName = "mt-1",
}: {
  ctaLabel?: string;
  unauthenticatedCtaClassName?: string;
  authenticatedCtaClassName?: string;
}) => {
  const { data: sessionData } = useSession();

  const { dispatch } = useStoreValue(state => state.shouldShowLoginModal);

  return sessionData ? (
    <div
      className={`flex items-center justify-end w-full ${authenticatedCtaClassName}`}
    >
      <UserNavigationMenu />
    </div>
  ) : (
    <div
      className={`flex items-center justify-center md:justify-end space-x-4 w-full ${unauthenticatedCtaClassName}`}
    >
      <Button
        onClick={() => dispatch(() => ({ shouldShowLoginModal: true }))}
        className='text-white'
      >
        <LogIn className='mr-2 h-4 w-4' />
        <span>{ctaLabel}</span>
      </Button>
    </div>
  );
};
