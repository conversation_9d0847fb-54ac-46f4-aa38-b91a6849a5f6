"use client";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import Image from "next/image";
import { useState } from "react";

import { useStoreValue } from "@/app/StoreContext";
import { useDeviceDetect } from "@/common/hooks/useDeviceDetect";
import { Button } from "@/components/ui/button";
import { useRouter, useSearchParams } from "next/navigation";
import { z } from "zod";
import { ScanQRCode } from "../scan-qr-code";
import { AppleSVG } from "./apple-svg";
import { useSession } from "./auth-provider";
import { GoogleSVG } from "./google-svg";
import { LoginForm, LoginSchema, LoginType } from "./login-form";
import { ResetPassword } from "./reset-password";

export function LoginModal({
  appUrls,
  configs,
  orgId,
  buttonColor,
}: {
  configs: Record<string, unknown>;
  appUrls: Record<string, string>;
  orgId: string;
  buttonColor?: string;
}) {
  const [isResetPassword, setIsResetPassword] = useState(false);

  const { isMobile } = useDeviceDetect();

  const { state: shouldShowLoginModal, dispatch } = useStoreValue(
    state => state.shouldShowLoginModal
  );

  const { signIn, isError, errorMessage } = useSession();

  const handleSubmit = async (data: z.infer<typeof LoginSchema>) => {
    signIn({
      email: data.email,
      password: data.password,
      orgId,
      isSalesForceLogin: data.isSalesForceLogin,
    });
  };

  const redirect = useRouter();

  const loginType = useSearchParams().get("loginType") as LoginType;

  return (
    <Dialog
      open={shouldShowLoginModal}
      onOpenChange={() => {
        dispatch(() => ({ shouldShowLoginModal: false }));
        if (isResetPassword) {
          setIsResetPassword(false);
        }
      }}
    >
      <DialogContent className='lg:max-w-[800px] grid lg:grid-cols-2 mt-4'>
        <Image
          src={
            (configs?.class_checkin as { background_image_url: string })
              ?.background_image_url
          }
          alt='login-image'
          className='hidden lg:block h-full w-full object-cover'
          width={500}
          height={500}
        />

        <div>
          <DialogHeader>
            <DialogTitle style={{ color: buttonColor }} className='text-center'>
              {isResetPassword
                ? "Reset your password"
                : "Login to Make Reservation"}
            </DialogTitle>
          </DialogHeader>
          {isResetPassword ? (
            <ResetPassword />
          ) : (
            <LoginForm
              handleSubmit={handleSubmit}
              isError={isError}
              errorMessage={errorMessage}
            />
          )}

          {loginType !== "salesforce" && (
            <div className='flex justify-center items-center text-center'>
              {!isResetPassword && (
                <Button
                  onClick={() => setIsResetPassword(prev => !prev)}
                  variant='ghost'
                  className='text-center items-center'
                >
                  First time login / Forgot password
                </Button>
              )}
            </div>
          )}

          <DialogDescription className='mt-4 text-center'>
            <strong className='mb-2 block'>Download Our Mobile App</strong>
            Easily manage your reservations with our mobile app
          </DialogDescription>
          {isMobile ? (
            <div className='flex gap-2 items-center justify-center'>
              <AppleSVG
                className='cursor-pointer'
                onClick={() => redirect.push(String(appUrls?.ios))}
              />
              <GoogleSVG
                className='cursor-pointer'
                onClick={() => redirect.push(String(appUrls?.android))}
              />
            </div>
          ) : (
            <ScanQRCode
              androidUrl={String(appUrls?.android)}
              iosUrl={String(appUrls?.ios)}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
