"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useEffect } from "react";

import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import { FaRegEnvelope } from "react-icons/fa";
import { IoLockClosedOutline } from "react-icons/io5";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useParams, useSearchParams } from "next/navigation";

export type LoginType = "salesforce" | "upace" | "all";

export const LoginSchema = z.object({
  email: z.string().min(2, {
    message: "Email is required",
  }),
  password: z.string().min(1, {
    message: "Password is required",
  }),
  isSalesForceLogin: z.boolean().optional(),
});

export function LoginForm({
  handleSubmit,
  isError,
  isLoading,
  placeholder = "Login",
  className,
  errorMessage,
  title,
}: Readonly<{
  handleSubmit: (data: z.infer<typeof LoginSchema>) => void;
  placeholder?: string;
  isError?: boolean;
  isLoading?: boolean;
  className?: string;
  errorMessage?: string | null;
  title?: string;
}>) {
  const loginType = useSearchParams().get("loginType") as LoginType;

  const formControl = useForm<z.infer<typeof LoginSchema>>({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      email: "",
      password: "",
      isSalesForceLogin: loginType === "salesforce",
    },
  });

  async function onSubmit(data: z.infer<typeof LoginSchema>) {
    handleSubmit(data);
  }

  useEffect(() => {
    if (isError) {
      formControl.reset();
      formControl.setError("root", {
        message: errorMessage || "Your email or password is invalid",
      });
    }
  }, [formControl, isError, errorMessage]);

  const errors = formControl.formState.errors;

  const orgId = useParams().orgId;

  return (
    <Form {...formControl}>
      <form
        onSubmit={formControl.handleSubmit(onSubmit)}
        className=' grid gap-4 pt-8'
      >
        {title && <p className='text-sm text-black font-bold'>{title}</p>}
        {errors?.root && (
          <FormMessage className='text-center'>
            {errors?.root?.message}
          </FormMessage>
        )}
        <FormField
          control={formControl.control}
          name='email'
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className='relative col-span-3'>
                  <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                    <FaRegEnvelope className='text-gray-500' />
                  </div>
                  <Input
                    className='pl-8 text-base'
                    placeholder='Enter Email Address'
                    type='email'
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={formControl.control}
          name='password'
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className='relative col-span-3'>
                  <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                    <IoLockClosedOutline className='text-gray-500' />
                  </div>
                  <Input
                    className='pl-8 text-base'
                    placeholder='Password'
                    type='password'
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* Temporal solution to show salesforce login only for orgId 33 */}
        {orgId === "33" && (
          <FormField
            control={formControl.control}
            name='isSalesForceLogin'
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className='flex items-start gap-3'>
                    <Checkbox
                      id='salesforce'
                      {...field}
                      value=''
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={loginType === "salesforce"}
                    />
                    <div className='grid gap-2'>
                      <Label htmlFor='salesforce'>
                        Log In with YMCA North Community Account
                      </Label>
                    </div>
                  </div>
                </FormControl>
                <FormMessage />
                {/* Temporal solution to show salesforce login only for orgId 33 */}
                <div className='flex justify-center items-center text-center mt-2'>
                  <Button
                    onClick={() =>
                      window.open(
                        "https://ymcaofthenorth--yofthen.sandbox.my.site.com/recde/s/login/ForgotPassword"
                      )
                    }
                    variant='ghost'
                    className='text-center items-center'
                  >
                    Community Forgot Password link
                  </Button>
                </div>
              </FormItem>
            )}
          />
        )}
        <div className='flex justify-center'>
          <Button
            disabled={isLoading}
            type='submit'
            className={cn(
              "text-white bg-blue-600 mt-4 hover:bg-blue-600 w-full lg:w-20 flex items-center",
              className
            )}
          >
            {isLoading ? (
              <Loader2 className='mr-2 h-4 w-4 animate-spin' />
            ) : (
              placeholder
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
