"use client";

import { useStoreValue } from "@/app/StoreContext";
import { useLocalStorage } from "@/common/hooks/useLocalStorage";
import { createContext, useContext } from "react";
import { LoginUserResponse, useLoginQuery } from "./useLoginQuery";

type UserSession = {
  signIn: ({
    email,
    password,
    orgId,
    isSalesForceLogin,
  }: {
    email: string;
    password: string;
    orgId?: string;
    isSalesForceLogin?: boolean;
  }) => void;
  data?: LoginUserResponse | undefined;
  signOut: () => void;
  isError?: boolean;
  errorMessage?: string | null;
};

const CurrentUserContext = createContext<UserSession>({
  signIn: () => {},
  signOut: () => {},
});

const UPACE_TOKEN = "__upace";

export const useSession = () => useContext(CurrentUserContext);

export const getSession = async () => {
  const record = await localStorage.getItem(UPACE_TOKEN);
  if (record) {
    return JSON.parse(record) as LoginUserResponse;
  }
  return undefined;
};

export const signOut = async () => await localStorage.removeItem(UPACE_TOKEN);

export const AuthContextProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { dispatch } = useStoreValue();

  const [value, setValue, removeValue] = useLocalStorage<
    LoginUserResponse | undefined
  >(UPACE_TOKEN, undefined);

  const {
    mutateAsync: signIn,
    isError,
    error,
  } = useLoginQuery(rec => {
    dispatch(() => ({ shouldShowLoginModal: false }));
    if (rec) {
      setValue(() => ({
        ...rec,
        name: `${rec?.first_name} ${rec?.last_name}`,
      }));
    }
  });

  return (
    <CurrentUserContext.Provider
      value={{
        signIn,
        data: value,
        isError,
        errorMessage: error?.message,
        signOut: removeValue,
      }}
    >
      {children}
    </CurrentUserContext.Provider>
  );
};
