"use client";
import { cn } from "@/lib/utils";
import { useFormContext } from "react-hook-form";
import { FormControl, FormField, FormItem, FormMessage } from "../ui/form";
import { BaseSelect, Option } from "../ui/select";

interface FormSelectProps {
  name: string;
  placeholder: string;
  options?: Option[];
  multiple?: boolean;
  disabled?: boolean;
  className?: string;
  wrapperClassName?: string;
  label?: string;
  selectWidth?: string;
  formItemClassName?: string;
  onChange?: (value: Option) => void;
}

export const FormSelect: React.FC<FormSelectProps> = ({
  name,
  placeholder,
  options,
  multiple,
  disabled,
  className,
  wrapperClassName,
  label,
  selectWidth = "lg:w-[200px]",
  formItemClassName = "",
  onChange,
}) => {
  const { control } = useFormContext();
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={formItemClassName}>
          <FormControl>
            <BaseSelect
              {...field}
              label={label}
              onChange={value => {
                if (onChange) {
                  onChange(value as Option);
                } else {
                  field.onChange(value);
                }
              }}
              wrapperClassName={wrapperClassName}
              disabled={disabled}
              multiple={multiple}
              placeholder={placeholder}
              options={options}
              className={cn(selectWidth, className)}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
