"use client";

import {
  addWeeks,
  format,
  formatDate,
  isAfter,
  lastDayOfWeek,
  parseISO,
  startOfWeek,
} from "date-fns";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useCallback, useRef } from "react";
import { Button } from "../ui/button";

import { useStoreValue } from "@/app/StoreContext";
import { useApplyStateToUrl } from "@/common/hooks/useApplyStateToUrl";
import { useSearchParams } from "next/navigation";

const dateFormat = "MMMM yyyy";

export const WeekDatePicker = () => {
  const today = useRef(parseISO(new Date().toISOString())).current;

  const dateParam = useSearchParams().get("start_date");

  const start_date = dateParam ? parseISO(dateParam) : today;

  const startWeek = startOfWeek(start_date, { weekStartsOn: 1 });
  const endWeek = lastDayOfWeek(start_date, { weekStartsOn: 1 });

  const applyStateToUrl = useApplyStateToUrl();

  const updateWeek = useCallback(
    (weeksToAdd: number) => {
      const newStartWeek = addWeeks(startWeek, weeksToAdd);
      applyStateToUrl({
        start_date: formatDate(newStartWeek, "yyyy-MM-dd"),
      });
    },
    [applyStateToUrl, startWeek]
  );

  const onDateClickHandle = (day: Date) => {
    applyStateToUrl({
      start_date: formatDate(
        startOfWeek(day, { weekStartsOn: 1 }),
        "yyyy-MM-dd"
      ),
    });
  };

  const canGoPrev = isAfter(startWeek, new Date());

  const { state: configs } = useStoreValue(store => store.configs);

  return (
    <div className='flex flex-col items-center'>
      <header className='flex py-4 text-3xl lg:text-4xl mb-2'>
        <div className='text-center items-center w-full'>
          <span>{format(startWeek, dateFormat)}</span>
        </div>
      </header>
      <div className='flex items-center'>
        <Button
          onClick={() => onDateClickHandle(today)}
          className='hidden lg:inline-flex justify-start'
          variant={"ghost"}
        >
          <ChevronLeft /> Back to this week
        </Button>
        <div className='flex items-center justify-center w-full'>
          <button
            title='previous week'
            type='button'
            disabled={!canGoPrev}
            onClick={() => updateWeek(-1)}
            style={{ color: configs?.accent_color }}
            className={`transition ease-in-out duration-150 ${!canGoPrev ? "opacity-50 cursor-not-allowed" : "hover:opacity-75"}`}
          >
            <ChevronLeft width={50} height={50} />
          </button>

          <p className='text-center pt-4 font-bold  mb-4'>
            {formatDate(startWeek, "MMM dd")} - {""}
            {formatDate(endWeek, "MMM dd")}
          </p>
          <button
            style={{ color: configs?.accent_color }}
            title='next week'
            type='button'
            onClick={() => updateWeek(1)}
          >
            <ChevronRight width={50} height={50} />
          </button>
        </div>
      </div>
    </div>
  );
};
