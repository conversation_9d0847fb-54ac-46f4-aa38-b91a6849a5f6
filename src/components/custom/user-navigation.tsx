"use client";

import { getInitials } from "@/app/classes/[orgId]/utils";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, LogOut } from "lucide-react";
import { useSession } from "./login-auth/auth-provider";

export function UserNavigationMenu() {
  const { signOut, data: sessionData } = useSession();

  const handleLogout = () => {
    signOut();
  };

  const fullName = sessionData?.first_name + " " + sessionData?.last_name;
  const email = sessionData?.email;

  const initials = getInitials(fullName);

  return (
    <div className='flex items-center justify-end'>
      <DropdownMenu>
        <DropdownMenuTrigger className='flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 border border-blue-400'>
          <Avatar className='h-7 w-7 bg-blue-600'>
            <AvatarFallback className='bg-blue-600 text-white text-sm font-semibold'>
              {initials}
            </AvatarFallback>
          </Avatar>
          <div className='hidden sm:flex flex-col items-start'>
            <span className='text-sm font-semibold text-gray-900 capitalize'>
              {fullName}
            </span>
          </div>
          <ChevronDown className='h-4 w-4 text-gray-500' />
        </DropdownMenuTrigger>

        <DropdownMenuContent
          align='end'
          className='w-64 mt-2 border border-gray-200 shadow-lg rounded-lg bg-white p-0'
        >
          <div className='p-4 border-b border-gray-100'>
            <div className='flex items-center space-x-3'>
              <Avatar className='h-10 w-10 bg-blue-600'>
                <AvatarFallback className='bg-blue-600 text-white text-base font-semibold'>
                  {initials}
                </AvatarFallback>
              </Avatar>
              <div className='flex flex-col'>
                <p className='text-sm font-semibold text-gray-900 capitalize'>
                  {fullName}
                </p>
                <p className='text-xs text-gray-500'>{email}</p>
              </div>
            </div>
          </div>

          <div className='p-2'>
            <DropdownMenuItem
              onClick={handleLogout}
              className='px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 cursor-pointer flex items-center space-x-2 rounded-md'
            >
              <LogOut className='h-4 w-4' />
              <span>Log out</span>
            </DropdownMenuItem>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
