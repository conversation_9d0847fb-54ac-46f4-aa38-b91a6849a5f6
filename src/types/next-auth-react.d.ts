// Type declarations for next-auth/react
declare module "next-auth/react" {
  import { Session } from "next-auth";

  export interface SignInOptions {
    callbackUrl?: string;
    redirect?: boolean;
  }

  export interface SignInResponse {
    error: string | null;
    status: number;
    ok: boolean;
    url: string | null;
  }

  export interface UseSessionOptions {
    required?: boolean;
    onUnauthenticated?: () => void;
  }

  export interface UseSessionResult {
    data: Session | null;
    status: "loading" | "authenticated" | "unauthenticated";
    update: (data: any) => Promise<Session>;
  }

  export function signIn(
    provider?: string,
    options?: SignInOptions
  ): Promise<SignInResponse>;
  export function signOut(options?: { callbackUrl?: string }): Promise<void>;
  export function useSession(options?: UseSessionOptions): UseSessionResult;
}
