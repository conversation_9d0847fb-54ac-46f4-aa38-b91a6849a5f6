import { useStoreValue } from "@/app/StoreContext";
import { useReserveMutation } from "@/app/classes/[orgId]/mutations/useReserveClass";
import { ClassDetailsResponse } from "@/app/classes/[orgId]/types";
import { formatDate } from "../common.utils";

export const useClassReserve = (
  data: ClassDetailsResponse,
  date?: string | null
) => {
  const { dispatch } = useStoreValue();

  const { mutate: handleReservation, isPending } = useReserveMutation(() =>
    dispatch(() => ({
      reservation: { ...data, date },
    }))
  );

  const isReservationAllowed = data?.allow_reservations;

  const handleReserve = (classType?: "live" | "virtual") => {
    handleReservation({
      date: formatDate(date),
      class_id: data.id,
      type: "class",
      is_virtual:
        (classType && classType === "virtual") || Boolean(data.is_virtual),
    });
  };

  return { isPending, isReservationAllowed, handleReserve };
};
