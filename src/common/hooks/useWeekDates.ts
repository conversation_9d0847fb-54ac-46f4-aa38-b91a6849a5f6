import { isValid, lastDayOfWeek, parseISO, startOfWeek } from "date-fns";
import { useSearchParams } from "next/navigation";

export function useWeekDates() {
  const searchParams = useSearchParams();
  const startDateParam = searchParams.get("start_date");

  let baseDate = new Date();
  if (startDateParam) {
    const parsedDate = parseISO(String(startDateParam));
    if (isValid(parsedDate)) {
      baseDate = parsedDate;
    }
  }

  const weekStart = startOfWeek(baseDate, { weekStartsOn: 1 });
  const weekEnd = lastDayOfWeek(weekStart, { weekStartsOn: 1 });

  return {
    weekStart,
    weekEnd,
  };
}
