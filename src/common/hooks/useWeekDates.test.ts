import { renderHook } from "@testing-library/react";
import { format } from "date-fns";
import { useSearchParams } from "next/navigation";
import { useWeekDates } from "./useWeekDates";

// Mock next/navigation
jest.mock("next/navigation", () => ({
  useSearchParams: jest.fn(() => ({
    get: jest.fn(),
  })),
}));

describe("useWeekDates", () => {
  const mockSearchParams = (startDate?: string) => {
    (useSearchParams as jest.Mock).mockImplementation(() => ({
      get: jest.fn(param => (param === "start_date" ? startDate : null)),
    }));
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return current week dates when no start_date param", () => {
    mockSearchParams(undefined);

    const { result } = renderHook(() => useWeekDates());

    const weekStart = format(result.current.weekStart, "EEEE");
    const weekEnd = format(result.current.weekEnd, "EEEE");

    expect(weekStart).toBe("Monday");
    expect(weekEnd).toBe("Sunday");
  });

  it("should use provided start_date from search params", () => {
    const testDate = "2024-01-03";
    mockSearchParams(testDate);

    const { result } = renderHook(() => useWeekDates());

    const weekStart = format(result.current.weekStart, "yyyy-MM-dd");
    const weekEnd = format(result.current.weekEnd, "yyyy-MM-dd");

    expect(weekStart).toBe("2024-01-01");
    expect(weekEnd).toBe("2024-01-07");
  });

  it("should handle invalid start_date gracefully", () => {
    mockSearchParams("invalid-date");

    const { result } = renderHook(() => useWeekDates());

    const weekStart = format(result.current.weekStart, "EEEE");
    const weekEnd = format(result.current.weekEnd, "EEEE");

    expect(weekStart).toBe("Monday");
    expect(weekEnd).toBe("Sunday");
  });

  it("should maintain week integrity across months", () => {
    const testDate = "2024-01-31";
    mockSearchParams(testDate);

    const { result } = renderHook(() => useWeekDates());

    const weekStart = format(result.current.weekStart, "yyyy-MM-dd");
    const weekEnd = format(result.current.weekEnd, "yyyy-MM-dd");

    expect(weekStart).toBe("2024-01-29");
    expect(weekEnd).toBe("2024-02-04");
  });

  it("should maintain week integrity across years", () => {
    const testDate = "2023-12-31";
    mockSearchParams(testDate);

    const { result } = renderHook(() => useWeekDates());

    const weekStart = format(result.current.weekStart, "yyyy-MM-dd");
    const weekEnd = format(result.current.weekEnd, "yyyy-MM-dd");

    expect(weekStart).toBe("2023-12-25");
    expect(weekEnd).toBe("2023-12-31");
  });
});
