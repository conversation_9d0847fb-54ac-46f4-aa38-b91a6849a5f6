import { addDays, format, subDays } from "date-fns";
import {
  DAYS_OF_WEEK,
  DEFAULT_DATE_FORMAT,
  formatDate,
  generateTimeOptions,
  isDateInPast,
} from "./common.utils";

describe("Common Utils", () => {
  describe("formatDate", () => {
    it("should format Date object correctly", () => {
      const date = new Date("2024-01-15T12:00:00Z");
      expect(formatDate(date)).toBe("2024-01-15");
    });

    it("should format date string correctly", () => {
      expect(formatDate("2024-01-15T12:00:00Z")).toBe("2024-01-15");
    });

    it("should handle undefined by using current date", () => {
      const today = format(new Date(), DEFAULT_DATE_FORMAT);
      expect(formatDate(undefined)).toBe(today);
    });

    it("should handle null by using current date", () => {
      const today = format(new Date(), DEFAULT_DATE_FORMAT);
      expect(formatDate(null)).toBe(today);
    });
  });

  describe("generateTimeOptions", () => {
    const timeOptions = generateTimeOptions();

    it("should generate correct number of options", () => {
      expect(timeOptions).toHaveLength(18);
    });

    it("should start at 6am", () => {
      expect(timeOptions[0]).toEqual({ label: "6:00am", value: "06:00" });
    });

    it("should handle noon correctly", () => {
      expect(timeOptions[6]).toEqual({ label: "12:00pm", value: "12:00" });
    });

    it("should handle pm times correctly", () => {
      expect(timeOptions[7]).toEqual({ label: "1:00pm", value: "13:00" });
    });

    it("should end at 11pm", () => {
      expect(timeOptions[17]).toEqual({ label: "11:00pm", value: "23:00" });
    });
  });

  describe("DAYS_OF_WEEK", () => {
    it("should have correct number of days", () => {
      expect(DAYS_OF_WEEK).toHaveLength(7);
    });

    it("should have correct format for each day", () => {
      DAYS_OF_WEEK.forEach(day => {
        expect(day).toHaveProperty("value");
        expect(day).toHaveProperty("label");
        expect(day.value).toBe(day.value.toLowerCase());
        expect(day.label).toBe(day.label.toUpperCase());
      });
    });

    it("should start with Monday", () => {
      expect(DAYS_OF_WEEK[0]).toEqual({ value: "mon", label: "MONDAY" });
    });

    it("should end with Sunday", () => {
      expect(DAYS_OF_WEEK[6]).toEqual({ value: "sun", label: "SUNDAY" });
    });
  });

  describe("isDateInPast", () => {
    const today = new Date();
    const tomorrow = addDays(today, 1);
    const yesterday = subDays(today, 1);
    const todayStr = format(today, DEFAULT_DATE_FORMAT);
    const tomorrowStr = format(tomorrow, DEFAULT_DATE_FORMAT);
    const yesterdayStr = format(yesterday, DEFAULT_DATE_FORMAT);

    it("should return true for past dates", () => {
      expect(isDateInPast(yesterdayStr)).toBe(true);
    });

    it("should return false for future dates", () => {
      expect(isDateInPast(tomorrowStr)).toBe(false);
    });

    it("should return false for today", () => {
      expect(isDateInPast(todayStr)).toBe(false);
    });

    it("should handle dates far in the past", () => {
      expect(isDateInPast("2020-01-01")).toBe(true);
    });

    it("should handle dates far in the future", () => {
      expect(isDateInPast("2030-12-31")).toBe(false);
    });

    it("should handle edge case of midnight", () => {
      const midnightToday = format(
        new Date(
          today.getFullYear(),
          today.getMonth(),
          today.getDate(),
          0,
          0,
          0
        ),
        DEFAULT_DATE_FORMAT
      );
      expect(isDateInPast(midnightToday)).toBe(false);
    });

    it("should handle edge case of end of day", () => {
      const endOfToday = format(
        new Date(
          today.getFullYear(),
          today.getMonth(),
          today.getDate(),
          23,
          59,
          59
        ),
        DEFAULT_DATE_FORMAT
      );
      expect(isDateInPast(endOfToday)).toBe(false);
    });

    it("should throw error for invalid date format", () => {
      expect(() => isDateInPast("invalid-date")).toThrow();
      expect(() => isDateInPast("2024/01/15")).toThrow();
    });
  });
});
