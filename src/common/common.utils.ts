import { endOfDay, format, isPast, isValid, parseISO } from "date-fns";

export const DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

export const formatDate = (date?: string | Date | undefined | null) => {
  if (date && typeof date === "object" && date instanceof Date) {
    return format(parseISO(date.toISOString()), DEFAULT_DATE_FORMAT);
  }

  const isoDate = date ? parseISO(date) : parseISO(new Date().toISOString());
  return format(isoDate, DEFAULT_DATE_FORMAT);
};

export const generateTimeOptions = () => {
  const hours = Array.from({ length: 18 }, (_, i) => (i + 6) % 24);

  return hours.flatMap(hour => {
    const hour24 = hour.toString().padStart(2, "0");
    const hour12 = (((hour + 11) % 12) + 1).toString();
    const period = hour < 12 ? "am" : "pm";
    const value = `${hour24}:00`;
    const label = `${hour12}:00${period}`;

    return { label, value };
  });
};

export const DAYS_OF_WEEK = [
  { value: "mon", label: "MONDAY" },
  { value: "tue", label: "TUESDAY" },
  { value: "wed", label: "WEDNESDAY" },
  { value: "thu", label: "THURSDAY" },
  { value: "fri", label: "FRIDAY" },
  { value: "sat", label: "SATURDAY" },
  { value: "sun", label: "SUNDAY" },
] as const;

/**
 * Checks if a given date is in the past
 * @param date - Date string in ISO format (YYYY-MM-DD)
 * @returns true if the date is in the past, false otherwise
 * @throws Error if the date string is not a valid date
 * @example
 * isDateInPast("2023-01-01") // true
 * isDateInPast("2025-12-31") // false
 */
export const isDateInPast = (date: string): boolean => {
  const parsedDate = parseISO(date);
  if (!isValid(parsedDate)) {
    throw new Error(
      "Invalid date. Please provide a valid date in YYYY-MM-DD format."
    );
  }
  return isPast(endOfDay(parsedDate));
};
