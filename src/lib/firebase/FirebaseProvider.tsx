"use client";

import React, { createContext, useContext } from "react";
import { auth, logEvent } from "./config";

interface FirebaseContextType {
  auth: typeof auth;
  logEvent: typeof logEvent;
}

const FirebaseContext = createContext<FirebaseContextType | null>(null);

export function useFirebase() {
  const context = useContext(FirebaseContext);
  if (!context) {
    throw new Error("useFirebase must be used within a FirebaseProvider");
  }
  return context;
}

export function FirebaseProvider({ children }: { children: React.ReactNode }) {
  const value = {
    auth,
    logEvent,
  };

  return (
    <FirebaseContext.Provider value={value}>
      {children}
    </FirebaseContext.Provider>
  );
}
