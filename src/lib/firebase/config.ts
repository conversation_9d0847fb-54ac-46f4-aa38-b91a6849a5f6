import {
  Analytics,
  AnalyticsCallOptions,
  getAnalytics,
  isSupported,
  logEvent as logEventFirebase,
} from "firebase/analytics";
import { getApps, initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

export enum FirebaseEvent {
  EMBED_LOADED = "embed_loaded",
  USER_LOGIN_ATTEMPT = "user_login_attempt",
  USER_LOGIN_SUCCESS = "login_successful",
  USER_LOGIN_FAILED = "login_failed",
  SCHEDULE_VIEW_OPENED = "schedule_view_opened",
  CLASS_RESERVED = "class_reserved",
  CLASS_RESERVED_FAILED = "class_reserved_failed",
}

let analytics: Analytics | null = null;

function logEvent(
  eventName: string,
  eventParams?: { [key: string]: any },
  options?: AnalyticsCallOptions
) {
  if (analytics) {
    logEventFirebase(analytics, eventName, eventParams, options);
  }
}

const app = !getApps().length ? initializeApp(firebaseConfig) : getApps()[0];
const db = getFirestore(app as any);
const auth = getAuth(app);

if (typeof window !== "undefined") {
  isSupported().then(
    yes => yes && ((analytics = getAnalytics(app)) as Analytics)
  );
}

export { analytics, app, auth, db, logEvent };
