import { BASE_API_URL_CLIENT } from "@/app/classes/[orgId]/actions/constant";
import { getSession } from "@/components/custom/login-auth/auth-provider";
import ky from "ky";

export const api = ky.create({
  prefixUrl: BASE_API_URL_CLIENT,
  headers: {
    Accept: "application/json",
    "Content-Type": "application/json",
  },
  // use a hook to add the authorization header before each request
  hooks: {
    beforeRequest: [
      async request => {
        const session = await getSession();

        request.headers.set(
          "Authorization",
          session?.token ? `Bearer ${session?.token}` : ""
        );
      },
    ],
  },
});
