import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function extractIdFromUrl(url: string | null) {
  if (!url) {
    return "";
  }
  const match = url?.match(/\/(\d+)(\/|\?|$)/);
  return match ? match[1] : "";
}

export function extractFontDetails(fontStyle?: string) {
  if (!fontStyle) {
    return ["", ""];
  }
  return fontStyle.split("::");
}

export function updateUrlLastSegment(url: string, newValue: string): string {
  // This regex matches the last segment after the last slash that is not followed by any other slash
  // eslint-disable-next-line no-useless-escape
  return url.replace(/\/[^\/]+$/, `/${newValue}`);
}
